{"version": 3, "sources": ["../../src/repository/BaseEntity.ts"], "names": [], "mappings": ";;;AAaA,qDAAiD;AAMjD;;GAEG;AACH,MAAa,UAAU;IAUnB,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;;OAGG;IACH,KAAK;QACD,MAAM,UAAU,GAAG,IAAI,CAAC,WAAgC,CAAA;QACxD,OAAO,UAAU,CAAC,aAAa,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;IACjD,CAAC;IAED;;;OAGG;IACH,IAAI,CAAC,OAAqB;QACtB,MAAM,UAAU,GAAG,IAAI,CAAC,WAAgC,CAAA;QACxD,OAAO,UAAU,CAAC,aAAa,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;IACzD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,OAAuB;QAC1B,MAAM,UAAU,GAAG,IAAI,CAAC,WAAgC,CAAA;QACxD,OAAO,UAAU,CAAC,aAAa,EAAE,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAkB,CAAA;IAC5E,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,OAAqB;QAC5B,MAAM,UAAU,GAAG,IAAI,CAAC,WAAgC,CAAA;QACxD,OAAO,UAAU,CAAC,aAAa,EAAE,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;IAC/D,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,OAAqB;QACzB,MAAM,UAAU,GAAG,IAAI,CAAC,WAAgC,CAAA;QACxD,OAAO,UAAU,CAAC,aAAa,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;IAC5D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM;QACR,MAAM,UAAU,GAAG,IAAI,CAAC,WAAgC,CAAA;QACxD,MAAM,EAAE,GAAG,UAAU,CAAC,aAAa,EAAE,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA;QACnE,IAAI,CAAC,EAAE,EAAE,CAAC;YACN,MAAM,IAAI,KAAK,CACX,oDAAoD,CACvD,CAAA;QACL,CAAC;QACD,MAAM,cAAc,GAAe,MAAM,UAAU;aAC9C,aAAa,EAAE;aACf,eAAe,CAAC,EAAE,CAAC,CAAA;QAExB,yBAAW,CAAC,MAAM,CAAC,IAAI,EAAE,cAAc,CAAC,CAAA;IAC5C,CAAC;IAED,4EAA4E;IAC5E,wBAAwB;IACxB,4EAA4E;IAE5E;;OAEG;IACH,MAAM,CAAC,aAAa,CAAC,UAA6B;QAC9C,IAAI,CAAC,UAAU,GAAG,UAAU,CAAA;IAChC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,aAAa;QAGhB,MAAM,UAAU,GAAI,IAA0B,CAAC,UAAU,CAAA;QACzD,IAAI,CAAC,UAAU;YACX,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAA;QAC7D,OAAO,UAAU,CAAC,aAAa,CAAI,IAAI,CAAC,CAAA;IAC5C,CAAC;IAED;;;;OAIG;IACH,MAAM,KAAK,MAAM;QACb,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC,MAAM,CAAA;IACtC,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,KAAK,CAAC,MAAkB;QAC3B,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;IAC7C,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAER,MAAS;QAET,OAAO,IAAI,CAAC,aAAa,EAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;IAChD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,kBAAkB,CAErB,KAAc;QAEd,OAAO,IAAI,CAAC,aAAa,EAAK,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAA;IAC5D,CAAC;IA2BD;;;OAGG;IACH,MAAM,CAAC,MAAM,CAET,gBAAsB;QAEtB,OAAO,IAAI,CAAC,aAAa,EAAK,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAA;IAC3D,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAER,eAAkB,EAClB,GAAG,WAA6B;QAEhC,OAAO,IAAI,CAAC,aAAa,EAAK,CAAC,KAAK,CAChC,eAAe,EACf,GAAG,WAAW,CACZ,CAAA;IACV,CAAC;IAED;;;;;;;;OAQG;IACH,MAAM,CAAC,OAAO,CAEV,UAA0B;QAE1B,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa,EAAK,CAAA;QAC9C,OAAO,cAAc,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA;IAC7C,CAAC;IAsBD;;OAEG;IACH,MAAM,CAAC,IAAI,CAEP,gBAAmD,EACnD,OAAqB;QAErB,OAAO,IAAI,CAAC,aAAa,EAAK,CAAC,IAAI,CAAC,gBAAuB,EAAE,OAAO,CAAC,CAAA;IACzE,CAAC;IAoBD;;OAEG;IACH,MAAM,CAAC,MAAM,CAET,gBAAyB,EACzB,OAAuB;QAEvB,OAAO,IAAI,CAAC,aAAa,EAAK,CAAC,MAAM,CAAC,gBAAuB,EAAE,OAAO,CAAC,CAAA;IAC3E,CAAC;IAoBD;;OAEG;IACH,MAAM,CAAC,UAAU,CAEb,gBAAyB,EACzB,OAAqB;QAErB,OAAO,IAAI,CAAC,aAAa,EAAK,CAAC,UAAU,CACrC,gBAAuB,EACvB,OAAO,CACV,CAAA;IACL,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,MAAM,CAET,MAA+D;QAE/D,OAAO,IAAI,CAAC,aAAa,EAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;IACjD,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,MAAM,CAET,QASyB,EACzB,aAAwC;QAExC,OAAO,IAAI,CAAC,aAAa,EAAK,CAAC,MAAM,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAA;IAClE,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,MAAM,CAET,gBAEiC,EACjC,sBAAmD;QAEnD,OAAO,IAAI,CAAC,aAAa,EAAK,CAAC,MAAM,CACjC,gBAAgB,EAChB,sBAAsB,CACzB,CAAA;IACL,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,MAAM,CAET,QASyB;QAEzB,OAAO,IAAI,CAAC,aAAa,EAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;IACnD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,MAAM,CAET,OAA4B;QAE5B,OAAO,IAAI,CAAC,aAAa,EAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;IAClD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,QAAQ,CAEX,KAA0B;QAE1B,OAAO,IAAI,CAAC,aAAa,EAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;IAClD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAER,OAA4B;QAE5B,OAAO,IAAI,CAAC,aAAa,EAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;IACjD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,OAAO,CAEV,KAA0B;QAE1B,OAAO,IAAI,CAAC,aAAa,EAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IACjD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,GAAG,CAEN,UAAqC,EACrC,KAA0B;QAE1B,OAAO,IAAI,CAAC,aAAa,EAAK,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,CAAC,CAAA;IACzD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,OAAO,CAEV,UAAqC,EACrC,KAA0B;QAE1B,OAAO,IAAI,CAAC,aAAa,EAAK,CAAC,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAAA;IAC7D,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,OAAO,CAEV,UAAqC,EACrC,KAA0B;QAE1B,OAAO,IAAI,CAAC,aAAa,EAAK,CAAC,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAAA;IAC7D,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,OAAO,CAEV,UAAqC,EACrC,KAA0B;QAE1B,OAAO,IAAI,CAAC,aAAa,EAAK,CAAC,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAAA;IAC7D,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,IAAI,CAEP,OAA4B;QAE5B,OAAO,IAAI,CAAC,aAAa,EAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;IAChD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,MAAM,CAET,KAA0B;QAE1B,OAAO,IAAI,CAAC,aAAa,EAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;IAChD,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,YAAY,CAEf,OAA4B;QAE5B,OAAO,IAAI,CAAC,aAAa,EAAK,CAAC,YAAY,CAAC,OAAO,CAAC,CAAA;IACxD,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,cAAc,CAEjB,KAA0B;QAE1B,OAAO,IAAI,CAAC,aAAa,EAAK,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;IACxD,CAAC;IAED;;;;;;;;;OASG;IACH,MAAM,CAAC,SAAS,CAEZ,GAAU;QAEV,OAAO,IAAI,CAAC,aAAa,EAAK,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;IACjD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,OAAO,CAEV,OAA0B;QAE1B,OAAO,IAAI,CAAC,aAAa,EAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;IACnD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,SAAS,CAEZ,KAA0B;QAE1B,OAAO,IAAI,CAAC,aAAa,EAAK,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;IACnD,CAAC;IAED;;;;;;;;OAQG;IACH,MAAM,CAAC,WAAW,CAEd,EAAqC;QAErC,OAAO,IAAI,CAAC,aAAa,EAAK,CAAC,WAAW,CAAC,EAAE,CAAC,CAAA;IAClD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,aAAa,CAEhB,OAA0B;QAE1B,OAAO,IAAI,CAAC,aAAa,EAAK,CAAC,aAAa,CAAC,OAAO,CAAC,CAAA;IACzD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,eAAe,CAElB,KAA0B;QAE1B,OAAO,IAAI,CAAC,aAAa,EAAK,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;IACzD,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,KAAK,CAER,KAAa,EACb,UAAkB;QAElB,OAAO,IAAI,CAAC,aAAa,EAAK,CAAC,KAAK,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;IAC3D,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK;QAGR,OAAO,IAAI,CAAC,aAAa,EAAK,CAAC,KAAK,EAAE,CAAA;IAC1C,CAAC;CACJ;AA9kBD,gCA8kBC", "file": "BaseEntity.js", "sourcesContent": ["import { Repository } from \"./Repository\"\nimport { FindOptionsWhere } from \"../find-options/FindOptionsWhere\"\nimport { DeepPartial } from \"../common/DeepPartial\"\nimport { SaveOptions } from \"./SaveOptions\"\nimport { FindOneOptions } from \"../find-options/FindOneOptions\"\nimport { RemoveOptions } from \"./RemoveOptions\"\nimport { FindManyOptions } from \"../find-options/FindManyOptions\"\nimport { DataSource } from \"../data-source\"\nimport { SelectQueryBuilder } from \"../query-builder/SelectQueryBuilder\"\nimport { InsertResult } from \"../query-builder/result/InsertResult\"\nimport { UpdateResult } from \"../query-builder/result/UpdateResult\"\nimport { DeleteResult } from \"../query-builder/result/DeleteResult\"\nimport { ObjectId } from \"../driver/mongodb/typings\"\nimport { ObjectUtils } from \"../util/ObjectUtils\"\nimport { QueryDeepPartialEntity } from \"../query-builder/QueryPartialEntity\"\nimport { UpsertOptions } from \"./UpsertOptions\"\nimport { EntityTarget } from \"../common/EntityTarget\"\nimport { PickKeysByType } from \"../common/PickKeysByType\"\n\n/**\n * Base abstract entity for all entities, used in ActiveRecord patterns.\n */\nexport class BaseEntity {\n    // -------------------------------------------------------------------------\n    // Private Static Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * DataSource used in all static methods of the BaseEntity.\n     */\n    private static dataSource: DataSource | null\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Checks if entity has an id.\n     * If entity composite compose ids, it will check them all.\n     */\n    hasId(): boolean {\n        const baseEntity = this.constructor as typeof BaseEntity\n        return baseEntity.getRepository().hasId(this)\n    }\n\n    /**\n     * Saves current entity in the database.\n     * If entity does not exist in the database then inserts, otherwise updates.\n     */\n    save(options?: SaveOptions): Promise<this> {\n        const baseEntity = this.constructor as typeof BaseEntity\n        return baseEntity.getRepository().save(this, options)\n    }\n\n    /**\n     * Removes current entity from the database.\n     */\n    remove(options?: RemoveOptions): Promise<this> {\n        const baseEntity = this.constructor as typeof BaseEntity\n        return baseEntity.getRepository().remove(this, options) as Promise<this>\n    }\n\n    /**\n     * Records the delete date of current entity.\n     */\n    softRemove(options?: SaveOptions): Promise<this> {\n        const baseEntity = this.constructor as typeof BaseEntity\n        return baseEntity.getRepository().softRemove(this, options)\n    }\n\n    /**\n     * Recovers a given entity in the database.\n     */\n    recover(options?: SaveOptions): Promise<this> {\n        const baseEntity = this.constructor as typeof BaseEntity\n        return baseEntity.getRepository().recover(this, options)\n    }\n\n    /**\n     * Reloads entity data from the database.\n     */\n    async reload(): Promise<void> {\n        const baseEntity = this.constructor as typeof BaseEntity\n        const id = baseEntity.getRepository().metadata.getEntityIdMap(this)\n        if (!id) {\n            throw new Error(\n                `Entity doesn't have id-s set, cannot reload entity`,\n            )\n        }\n        const reloadedEntity: BaseEntity = await baseEntity\n            .getRepository()\n            .findOneByOrFail(id)\n\n        ObjectUtils.assign(this, reloadedEntity)\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Static Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Sets DataSource to be used by entity.\n     */\n    static useDataSource(dataSource: DataSource | null) {\n        this.dataSource = dataSource\n    }\n\n    /**\n     * Gets current entity's Repository.\n     */\n    static getRepository<T extends BaseEntity>(\n        this: { new (): T } & typeof BaseEntity,\n    ): Repository<T> {\n        const dataSource = (this as typeof BaseEntity).dataSource\n        if (!dataSource)\n            throw new Error(`DataSource is not set for this entity.`)\n        return dataSource.getRepository<T>(this)\n    }\n\n    /**\n     * Returns object that is managed by this repository.\n     * If this repository manages entity from schema,\n     * then it returns a name of that schema instead.\n     */\n    static get target(): EntityTarget<any> {\n        return this.getRepository().target\n    }\n\n    /**\n     * Checks entity has an id.\n     * If entity composite compose ids, it will check them all.\n     */\n    static hasId(entity: BaseEntity): boolean {\n        return this.getRepository().hasId(entity)\n    }\n\n    /**\n     * Gets entity mixed id.\n     */\n    static getId<T extends BaseEntity>(\n        this: { new (): T } & typeof BaseEntity,\n        entity: T,\n    ): any {\n        return this.getRepository<T>().getId(entity)\n    }\n\n    /**\n     * Creates a new query builder that can be used to build a SQL query.\n     */\n    static createQueryBuilder<T extends BaseEntity>(\n        this: { new (): T } & typeof BaseEntity,\n        alias?: string,\n    ): SelectQueryBuilder<T> {\n        return this.getRepository<T>().createQueryBuilder(alias)\n    }\n\n    /**\n     * Creates a new entity instance.\n     */\n    static create<T extends BaseEntity>(\n        this: { new (): T } & typeof BaseEntity,\n    ): T\n\n    /**\n     * Creates a new entities and copies all entity properties from given objects into their new entities.\n     * Note that it copies only properties that present in entity schema.\n     */\n    static create<T extends BaseEntity>(\n        this: { new (): T } & typeof BaseEntity,\n        entityLikeArray: DeepPartial<T>[],\n    ): T[]\n\n    /**\n     * Creates a new entity instance and copies all entity properties from this object into a new entity.\n     * Note that it copies only properties that present in entity schema.\n     */\n    static create<T extends BaseEntity>(\n        this: { new (): T } & typeof BaseEntity,\n        entityLike: DeepPartial<T>,\n    ): T\n\n    /**\n     * Creates a new entity instance and copies all entity properties from this object into a new entity.\n     * Note that it copies only properties that present in entity schema.\n     */\n    static create<T extends BaseEntity>(\n        this: { new (): T } & typeof BaseEntity,\n        entityOrEntities?: any,\n    ) {\n        return this.getRepository<T>().create(entityOrEntities)\n    }\n\n    /**\n     * Merges multiple entities (or entity-like objects) into a given entity.\n     */\n    static merge<T extends BaseEntity>(\n        this: { new (): T } & typeof BaseEntity,\n        mergeIntoEntity: T,\n        ...entityLikes: DeepPartial<T>[]\n    ): T {\n        return this.getRepository<T>().merge(\n            mergeIntoEntity,\n            ...entityLikes,\n        ) as T\n    }\n\n    /**\n     * Creates a new entity from the given plain javascript object. If entity already exist in the database, then\n     * it loads it (and everything related to it), replaces all values with the new ones from the given object\n     * and returns this new entity. This new entity is actually a loaded from the db entity with all properties\n     * replaced from the new object.\n     *\n     * Note that given entity-like object must have an entity id / primary key to find entity by.\n     * Returns undefined if entity with given id was not found.\n     */\n    static preload<T extends BaseEntity>(\n        this: { new (): T } & typeof BaseEntity,\n        entityLike: DeepPartial<T>,\n    ): Promise<T | undefined> {\n        const thisRepository = this.getRepository<T>()\n        return thisRepository.preload(entityLike)\n    }\n\n    /**\n     * Saves all given entities in the database.\n     * If entities do not exist in the database then inserts, otherwise updates.\n     */\n    static save<T extends BaseEntity>(\n        this: { new (): T } & typeof BaseEntity,\n        entities: DeepPartial<T>[],\n        options?: SaveOptions,\n    ): Promise<T[]>\n\n    /**\n     * Saves a given entity in the database.\n     * If entity does not exist in the database then inserts, otherwise updates.\n     */\n    static save<T extends BaseEntity>(\n        this: { new (): T } & typeof BaseEntity,\n        entity: DeepPartial<T>,\n        options?: SaveOptions,\n    ): Promise<T>\n\n    /**\n     * Saves one or many given entities.\n     */\n    static save<T extends BaseEntity>(\n        this: { new (): T } & typeof BaseEntity,\n        entityOrEntities: DeepPartial<T> | DeepPartial<T>[],\n        options?: SaveOptions,\n    ) {\n        return this.getRepository<T>().save(entityOrEntities as any, options)\n    }\n\n    /**\n     * Removes a given entities from the database.\n     */\n    static remove<T extends BaseEntity>(\n        this: { new (): T } & typeof BaseEntity,\n        entities: T[],\n        options?: RemoveOptions,\n    ): Promise<T[]>\n\n    /**\n     * Removes a given entity from the database.\n     */\n    static remove<T extends BaseEntity>(\n        this: { new (): T } & typeof BaseEntity,\n        entity: T,\n        options?: RemoveOptions,\n    ): Promise<T>\n\n    /**\n     * Removes one or many given entities.\n     */\n    static remove<T extends BaseEntity>(\n        this: { new (): T } & typeof BaseEntity,\n        entityOrEntities: T | T[],\n        options?: RemoveOptions,\n    ) {\n        return this.getRepository<T>().remove(entityOrEntities as any, options)\n    }\n\n    /**\n     * Records the delete date of all given entities.\n     */\n    static softRemove<T extends BaseEntity>(\n        this: { new (): T } & typeof BaseEntity,\n        entities: T[],\n        options?: SaveOptions,\n    ): Promise<T[]>\n\n    /**\n     * Records the delete date of a given entity.\n     */\n    static softRemove<T extends BaseEntity>(\n        this: { new (): T } & typeof BaseEntity,\n        entity: T,\n        options?: SaveOptions,\n    ): Promise<T>\n\n    /**\n     * Records the delete date of one or many given entities.\n     */\n    static softRemove<T extends BaseEntity>(\n        this: { new (): T } & typeof BaseEntity,\n        entityOrEntities: T | T[],\n        options?: SaveOptions,\n    ) {\n        return this.getRepository<T>().softRemove(\n            entityOrEntities as any,\n            options,\n        )\n    }\n\n    /**\n     * Inserts a given entity into the database.\n     * Unlike save method executes a primitive operation without cascades, relations and other operations included.\n     * Executes fast and efficient INSERT query.\n     * Does not check if entity exist in the database, so query will fail if duplicate entity is being inserted.\n     */\n    static insert<T extends BaseEntity>(\n        this: { new (): T } & typeof BaseEntity,\n        entity: QueryDeepPartialEntity<T> | QueryDeepPartialEntity<T>[],\n    ): Promise<InsertResult> {\n        return this.getRepository<T>().insert(entity)\n    }\n\n    /**\n     * Updates entity partially. Entity can be found by a given conditions.\n     * Unlike save method executes a primitive operation without cascades, relations and other operations included.\n     * Executes fast and efficient UPDATE query.\n     * Does not check if entity exist in the database.\n     */\n    static update<T extends BaseEntity>(\n        this: { new (): T } & typeof BaseEntity,\n        criteria:\n            | string\n            | string[]\n            | number\n            | number[]\n            | Date\n            | Date[]\n            | ObjectId\n            | ObjectId[]\n            | FindOptionsWhere<T>,\n        partialEntity: QueryDeepPartialEntity<T>,\n    ): Promise<UpdateResult> {\n        return this.getRepository<T>().update(criteria, partialEntity)\n    }\n\n    /**\n     * Inserts a given entity into the database, unless a unique constraint conflicts then updates the entity\n     * Unlike save method executes a primitive operation without cascades, relations and other operations included.\n     * Executes fast and efficient INSERT ... ON CONFLICT DO UPDATE/ON DUPLICATE KEY UPDATE query.\n     */\n    static upsert<T extends BaseEntity>(\n        this: { new (): T } & typeof BaseEntity,\n        entityOrEntities:\n            | QueryDeepPartialEntity<T>\n            | QueryDeepPartialEntity<T>[],\n        conflictPathsOrOptions: string[] | UpsertOptions<T>,\n    ): Promise<InsertResult> {\n        return this.getRepository<T>().upsert(\n            entityOrEntities,\n            conflictPathsOrOptions,\n        )\n    }\n\n    /**\n     * Deletes entities by a given criteria.\n     * Unlike remove method executes a primitive operation without cascades, relations and other operations included.\n     * Executes fast and efficient DELETE query.\n     * Does not check if entity exist in the database.\n     */\n    static delete<T extends BaseEntity>(\n        this: { new (): T } & typeof BaseEntity,\n        criteria:\n            | string\n            | string[]\n            | number\n            | number[]\n            | Date\n            | Date[]\n            | ObjectId\n            | ObjectId[]\n            | FindOptionsWhere<T>,\n    ): Promise<DeleteResult> {\n        return this.getRepository<T>().delete(criteria)\n    }\n\n    /**\n     * Checks whether any entity exists that matches the given options.\n     */\n    static exists<T extends BaseEntity>(\n        this: { new (): T } & typeof BaseEntity,\n        options?: FindManyOptions<T>,\n    ): Promise<boolean> {\n        return this.getRepository<T>().exists(options)\n    }\n\n    /**\n     * Checks whether any entity exists that matches the given conditions.\n     */\n    static existsBy<T extends BaseEntity>(\n        this: { new (): T } & typeof BaseEntity,\n        where: FindOptionsWhere<T>,\n    ): Promise<boolean> {\n        return this.getRepository<T>().existsBy(where)\n    }\n\n    /**\n     * Counts entities that match given options.\n     */\n    static count<T extends BaseEntity>(\n        this: { new (): T } & typeof BaseEntity,\n        options?: FindManyOptions<T>,\n    ): Promise<number> {\n        return this.getRepository<T>().count(options)\n    }\n\n    /**\n     * Counts entities that match given WHERE conditions.\n     */\n    static countBy<T extends BaseEntity>(\n        this: { new (): T } & typeof BaseEntity,\n        where: FindOptionsWhere<T>,\n    ): Promise<number> {\n        return this.getRepository<T>().countBy(where)\n    }\n\n    /**\n     * Return the SUM of a column\n     */\n    static sum<T extends BaseEntity>(\n        this: { new (): T } & typeof BaseEntity,\n        columnName: PickKeysByType<T, number>,\n        where: FindOptionsWhere<T>,\n    ): Promise<number | null> {\n        return this.getRepository<T>().sum(columnName, where)\n    }\n\n    /**\n     * Return the AVG of a column\n     */\n    static average<T extends BaseEntity>(\n        this: { new (): T } & typeof BaseEntity,\n        columnName: PickKeysByType<T, number>,\n        where: FindOptionsWhere<T>,\n    ): Promise<number | null> {\n        return this.getRepository<T>().average(columnName, where)\n    }\n\n    /**\n     * Return the MIN of a column\n     */\n    static minimum<T extends BaseEntity>(\n        this: { new (): T } & typeof BaseEntity,\n        columnName: PickKeysByType<T, number>,\n        where: FindOptionsWhere<T>,\n    ): Promise<number | null> {\n        return this.getRepository<T>().minimum(columnName, where)\n    }\n\n    /**\n     * Return the MAX of a column\n     */\n    static maximum<T extends BaseEntity>(\n        this: { new (): T } & typeof BaseEntity,\n        columnName: PickKeysByType<T, number>,\n        where: FindOptionsWhere<T>,\n    ): Promise<number | null> {\n        return this.getRepository<T>().maximum(columnName, where)\n    }\n\n    /**\n     * Finds entities that match given options.\n     */\n    static find<T extends BaseEntity>(\n        this: { new (): T } & typeof BaseEntity,\n        options?: FindManyOptions<T>,\n    ): Promise<T[]> {\n        return this.getRepository<T>().find(options)\n    }\n\n    /**\n     * Finds entities that match given WHERE conditions.\n     */\n    static findBy<T extends BaseEntity>(\n        this: { new (): T } & typeof BaseEntity,\n        where: FindOptionsWhere<T>,\n    ): Promise<T[]> {\n        return this.getRepository<T>().findBy(where)\n    }\n\n    /**\n     * Finds entities that match given find options.\n     * Also counts all entities that match given conditions,\n     * but ignores pagination settings (from and take options).\n     */\n    static findAndCount<T extends BaseEntity>(\n        this: { new (): T } & typeof BaseEntity,\n        options?: FindManyOptions<T>,\n    ): Promise<[T[], number]> {\n        return this.getRepository<T>().findAndCount(options)\n    }\n\n    /**\n     * Finds entities that match given WHERE conditions.\n     * Also counts all entities that match given conditions,\n     * but ignores pagination settings (from and take options).\n     */\n    static findAndCountBy<T extends BaseEntity>(\n        this: { new (): T } & typeof BaseEntity,\n        where: FindOptionsWhere<T>,\n    ): Promise<[T[], number]> {\n        return this.getRepository<T>().findAndCountBy(where)\n    }\n\n    /**\n     * Finds entities by ids.\n     * Optionally find options can be applied.\n     *\n     * @deprecated use `findBy` method instead in conjunction with `In` operator, for example:\n     *\n     * .findBy({\n     *     id: In([1, 2, 3])\n     * })\n     */\n    static findByIds<T extends BaseEntity>(\n        this: { new (): T } & typeof BaseEntity,\n        ids: any[],\n    ): Promise<T[]> {\n        return this.getRepository<T>().findByIds(ids)\n    }\n\n    /**\n     * Finds first entity that matches given conditions.\n     */\n    static findOne<T extends BaseEntity>(\n        this: { new (): T } & typeof BaseEntity,\n        options: FindOneOptions<T>,\n    ): Promise<T | null> {\n        return this.getRepository<T>().findOne(options)\n    }\n\n    /**\n     * Finds first entity that matches given conditions.\n     */\n    static findOneBy<T extends BaseEntity>(\n        this: { new (): T } & typeof BaseEntity,\n        where: FindOptionsWhere<T>,\n    ): Promise<T | null> {\n        return this.getRepository<T>().findOneBy(where)\n    }\n\n    /**\n     * Finds first entity that matches given options.\n     *\n     * @deprecated use `findOneBy` method instead in conjunction with `In` operator, for example:\n     *\n     * .findOneBy({\n     *     id: 1 // where \"id\" is your primary column name\n     * })\n     */\n    static findOneById<T extends BaseEntity>(\n        this: { new (): T } & typeof BaseEntity,\n        id: string | number | Date | ObjectId,\n    ): Promise<T | null> {\n        return this.getRepository<T>().findOneById(id)\n    }\n\n    /**\n     * Finds first entity that matches given conditions.\n     */\n    static findOneOrFail<T extends BaseEntity>(\n        this: { new (): T } & typeof BaseEntity,\n        options: FindOneOptions<T>,\n    ): Promise<T> {\n        return this.getRepository<T>().findOneOrFail(options)\n    }\n\n    /**\n     * Finds first entity that matches given conditions.\n     */\n    static findOneByOrFail<T extends BaseEntity>(\n        this: { new (): T } & typeof BaseEntity,\n        where: FindOptionsWhere<T>,\n    ): Promise<T> {\n        return this.getRepository<T>().findOneByOrFail(where)\n    }\n\n    /**\n     * Executes a raw SQL query and returns a raw database results.\n     * Raw query execution is supported only by relational databases (MongoDB is not supported).\n     */\n    static query<T extends BaseEntity>(\n        this: { new (): T } & typeof BaseEntity,\n        query: string,\n        parameters?: any[],\n    ): Promise<any> {\n        return this.getRepository<T>().query(query, parameters)\n    }\n\n    /**\n     * Clears all the data from the given table/collection (truncates/drops it).\n     */\n    static clear<T extends BaseEntity>(\n        this: { new (): T } & typeof BaseEntity,\n    ): Promise<void> {\n        return this.getRepository<T>().clear()\n    }\n}\n"], "sourceRoot": ".."}