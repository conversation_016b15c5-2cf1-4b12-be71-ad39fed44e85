{"version": 3, "sources": ["../../src/repository/Repository.ts"], "names": [], "mappings": ";;;AAkBA,qDAAiD;AAEjD;;GAEG;AACH,MAAa,UAAU;IAsBnB,4EAA4E;IAC5E,YAAY;IACZ,4EAA4E;IAE5E;;OAEG;IACH,IAAI,QAAQ;QACR,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;IAC3D,CAAC;IAED,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YACI,MAA4B,EAC5B,OAAsB,EACtB,WAAyB;QAEzB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;QACtB,IAAI,CAAC,WAAW,GAAG,WAAW,CAAA;IAClC,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,kBAAkB,CACd,KAAc,EACd,WAAyB;QAEzB,OAAO,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAClC,IAAI,CAAC,QAAQ,CAAC,MAAa,EAC3B,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,EACjC,WAAW,IAAI,IAAI,CAAC,WAAW,CAClC,CAAA;IACL,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,MAAc;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;IAC3D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAc;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;IAC3D,CAAC;IAmBD;;;OAGG;IACH,MAAM,CACF,iCAE2B;QAE3B,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CACtB,IAAI,CAAC,QAAQ,CAAC,MAAa,EAC3B,iCAAwC,CAC3C,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CACD,eAAuB,EACvB,GAAG,WAAkC;QAErC,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CACrB,IAAI,CAAC,QAAQ,CAAC,MAAa,EAC3B,eAAe,EACf,GAAG,WAAW,CACjB,CAAA;IACL,CAAC;IAED;;;;;;;;OAQG;IACH,OAAO,CAAC,UAA+B;QACnC,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAa,EAAE,UAAU,CAAC,CAAA;IACxE,CAAC;IAsCD;;OAEG;IACH,IAAI,CACA,gBAAyB,EACzB,OAAqB;QAErB,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CACpB,IAAI,CAAC,QAAQ,CAAC,MAAa,EAC3B,gBAAuB,EACvB,OAAO,CACV,CAAA;IACL,CAAC;IAYD;;OAEG;IACH,MAAM,CACF,gBAAmC,EACnC,OAAuB;QAEvB,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CACtB,IAAI,CAAC,QAAQ,CAAC,MAAa,EAC3B,gBAAuB,EACvB,OAAO,CACV,CAAA;IACL,CAAC;IAkCD;;OAEG;IACH,UAAU,CACN,gBAAyB,EACzB,OAAqB;QAErB,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAC1B,IAAI,CAAC,QAAQ,CAAC,MAAa,EAC3B,gBAAuB,EACvB,OAAO,CACV,CAAA;IACL,CAAC;IAkCD;;OAEG;IACH,OAAO,CACH,gBAAyB,EACzB,OAAqB;QAErB,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CACvB,IAAI,CAAC,QAAQ,CAAC,MAAa,EAC3B,gBAAuB,EACvB,OAAO,CACV,CAAA;IACL,CAAC;IAED;;;;;OAKG;IACH,MAAM,CACF,MAEsC;QAEtC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAa,EAAE,MAAM,CAAC,CAAA;IACnE,CAAC;IAED;;;;;OAKG;IACH,MAAM,CACF,QAUgC,EAChC,aAA6C;QAE7C,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CACtB,IAAI,CAAC,QAAQ,CAAC,MAAM,EACpB,QAAQ,EACR,aAAa,CAChB,CAAA;IACL,CAAC;IAED;;;;;;OAMG;IACH,SAAS,CACL,aAA6C;QAE7C,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,aAAa,CAAC,CAAA;IACtE,CAAC;IAED;;;;OAIG;IACH,MAAM,CACF,gBAEsC,EACtC,sBAAwD;QAExD,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CACtB,IAAI,CAAC,QAAQ,CAAC,MAAa,EAC3B,gBAAgB,EAChB,sBAAsB,CACzB,CAAA;IACL,CAAC;IAED;;;;;OAKG;IACH,MAAM,CACF,QAUgC;QAEhC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAA;IAC9D,CAAC;IAED;;;;;;OAMG;IACH,SAAS;QACL,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;IACvD,CAAC;IAED;;;;;OAKG;IACH,UAAU,CACN,QAUgC;QAEhC,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAC1B,IAAI,CAAC,QAAQ,CAAC,MAAa,EAC3B,QAAe,CAClB,CAAA;IACL,CAAC;IAED;;;;;OAKG;IACH,OAAO,CACH,QAUgC;QAEhC,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CACvB,IAAI,CAAC,QAAQ,CAAC,MAAa,EAC3B,QAAe,CAClB,CAAA;IACL,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,OAAiC;QACnC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;IAC7D,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,OAAiC;QACpC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;IAC7D,CAAC;IAED;;OAEG;IACH,QAAQ,CACJ,KAA4D;QAE5D,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;IAC7D,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,OAAiC;QACnC,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;IAC5D,CAAC;IAED;;;OAGG;IACH,OAAO,CACH,KAA4D;QAE5D,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;IAC5D,CAAC;IAED;;OAEG;IACH,GAAG,CACC,UAA0C,EAC1C,KAA6D;QAE7D,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,UAAU,EAAE,KAAK,CAAC,CAAA;IACpE,CAAC;IAED;;OAEG;IACH,OAAO,CACH,UAA0C,EAC1C,KAA6D;QAE7D,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,UAAU,EAAE,KAAK,CAAC,CAAA;IACxE,CAAC;IAED;;OAEG;IACH,OAAO,CACH,UAA0C,EAC1C,KAA6D;QAE7D,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,UAAU,EAAE,KAAK,CAAC,CAAA;IACxE,CAAC;IAED;;OAEG;IACH,OAAO,CACH,UAA0C,EAC1C,KAA6D;QAE7D,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,UAAU,EAAE,KAAK,CAAC,CAAA;IACxE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,IAAI,CAAC,OAAiC;QACxC,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;IAC3D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM,CACR,KAA4D;QAE5D,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;IAC3D,CAAC;IAED;;;;OAIG;IACH,YAAY,CACR,OAAiC;QAEjC,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;IACnE,CAAC;IAED;;;;OAIG;IACH,cAAc,CACV,KAA4D;QAE5D,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;IACnE,CAAC;IAED;;;;;;;;;OASG;IACH,KAAK,CAAC,SAAS,CAAC,GAAU;QACtB,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;IAC5D,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,OAAO,CAAC,OAA+B;QACzC,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;IAC9D,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,SAAS,CACX,KAA4D;QAE5D,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;IAC9D,CAAC;IAED;;;;;;;;;OASG;IACH,KAAK,CAAC,WAAW,CACb,EAAqC;QAErC,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC,CAAA;IAC7D,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,aAAa,CAAC,OAA+B;QAC/C,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;IACpE,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,eAAe,CACjB,KAA4D;QAE5D,OAAO,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;IACpE,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAU,KAAa,EAAE,UAAkB;QAC5C,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;IAChD,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,GAAG,CACL,OAA6B,EAC7B,GAAG,MAAiB;QAEpB,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,IAAA,yBAAW,EAAC;YACtC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM;YACtC,OAAO,EAAE,OAAO;YAChB,WAAW,EAAE,MAAM;SACtB,CAAC,CAAA;QAEF,OAAO,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;IAC9C,CAAC;IAED;;;;;OAKG;IACH,KAAK;QACD,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;IACnD,CAAC;IAED;;OAEG;IACH,SAAS,CACL,UAAoC,EACpC,YAAoB,EACpB,KAAsB;QAEtB,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,CACzB,IAAI,CAAC,QAAQ,CAAC,MAAM,EACpB,UAAU,EACV,YAAY,EACZ,KAAK,CACR,CAAA;IACL,CAAC;IAED;;OAEG;IACH,SAAS,CACL,UAAoC,EACpC,YAAoB,EACpB,KAAsB;QAEtB,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,CACzB,IAAI,CAAC,QAAQ,CAAC,MAAM,EACpB,UAAU,EACV,YAAY,EACZ,KAAK,CACR,CAAA;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CACF,OAA6D;QAE7D,WAAW;QACX,eAAe;QACf,gBAAgB;QAChB,KAAK;QACL,MAAM,QAAQ,GAAQ,IAAI,CAAC,WAAW,CAAA;QACtC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,IAAI,CAAA;QAC7C,MAAM,UAAU,GAAG,KAAM,SAAQ,QAAQ;YACrC,YACI,MAA4B,EAC5B,OAAsB,EACtB,WAAyB;gBAEzB,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,WAAW,CAAC,CAAA;YACvC,CAAC;SACJ,CAAA;QACD,KAAK,MAAM,MAAM,IAAI,OAAO;YACxB,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,CAAA;QAClD,OAAO,IAAI,UAAU,CAAC,MAAM,EAAE,OAAO,EAAE,WAAW,CAAQ,CAAA;IAC9D,CAAC;CACJ;AA1uBD,gCA0uBC", "file": "Repository.js", "sourcesContent": ["import { FindManyOptions } from \"../find-options/FindManyOptions\"\nimport { ObjectLiteral } from \"../common/ObjectLiteral\"\nimport { FindOneOptions } from \"../find-options/FindOneOptions\"\nimport { DeepPartial } from \"../common/DeepPartial\"\nimport { SaveOptions } from \"./SaveOptions\"\nimport { RemoveOptions } from \"./RemoveOptions\"\nimport { EntityManager } from \"../entity-manager/EntityManager\"\nimport { QueryRunner } from \"../query-runner/QueryRunner\"\nimport { SelectQueryBuilder } from \"../query-builder/SelectQueryBuilder\"\nimport { DeleteResult } from \"../query-builder/result/DeleteResult\"\nimport { UpdateResult } from \"../query-builder/result/UpdateResult\"\nimport { InsertResult } from \"../query-builder/result/InsertResult\"\nimport { QueryDeepPartialEntity } from \"../query-builder/QueryPartialEntity\"\nimport { ObjectId } from \"../driver/mongodb/typings\"\nimport { FindOptionsWhere } from \"../find-options/FindOptionsWhere\"\nimport { UpsertOptions } from \"./UpsertOptions\"\nimport { EntityTarget } from \"../common/EntityTarget\"\nimport { PickKeysByType } from \"../common/PickKeysByType\"\nimport { buildSqlTag } from \"../util/SqlTagUtils\"\n\n/**\n * Repository is supposed to work with your entity objects. Find entities, insert, update, delete, etc.\n */\nexport class Repository<Entity extends ObjectLiteral> {\n    // -------------------------------------------------------------------------\n    // Public Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Entity target that is managed by this repository.\n     * If this repository manages entity from schema,\n     * then it returns a name of that schema instead.\n     */\n    readonly target: EntityTarget<Entity>\n\n    /**\n     * Entity Manager used by this repository.\n     */\n    readonly manager: EntityManager\n\n    /**\n     * Query runner provider used for this repository.\n     */\n    readonly queryRunner?: QueryRunner\n\n    // -------------------------------------------------------------------------\n    // Accessors\n    // -------------------------------------------------------------------------\n\n    /**\n     * Entity metadata of the entity current repository manages.\n     */\n    get metadata() {\n        return this.manager.connection.getMetadata(this.target)\n    }\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(\n        target: EntityTarget<Entity>,\n        manager: EntityManager,\n        queryRunner?: QueryRunner,\n    ) {\n        this.target = target\n        this.manager = manager\n        this.queryRunner = queryRunner\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates a new query builder that can be used to build a SQL query.\n     */\n    createQueryBuilder(\n        alias?: string,\n        queryRunner?: QueryRunner,\n    ): SelectQueryBuilder<Entity> {\n        return this.manager.createQueryBuilder<Entity>(\n            this.metadata.target as any,\n            alias || this.metadata.targetName,\n            queryRunner || this.queryRunner,\n        )\n    }\n\n    /**\n     * Checks if entity has an id.\n     * If entity composite compose ids, it will check them all.\n     */\n    hasId(entity: Entity): boolean {\n        return this.manager.hasId(this.metadata.target, entity)\n    }\n\n    /**\n     * Gets entity mixed id.\n     */\n    getId(entity: Entity): any {\n        return this.manager.getId(this.metadata.target, entity)\n    }\n\n    /**\n     * Creates a new entity instance.\n     */\n    create(): Entity\n\n    /**\n     * Creates new entities and copies all entity properties from given objects into their new entities.\n     * Note that it copies only properties that are present in entity schema.\n     */\n    create(entityLikeArray: DeepPartial<Entity>[]): Entity[]\n\n    /**\n     * Creates a new entity instance and copies all entity properties from this object into a new entity.\n     * Note that it copies only properties that are present in entity schema.\n     */\n    create(entityLike: DeepPartial<Entity>): Entity\n\n    /**\n     * Creates a new entity instance or instances.\n     * Can copy properties from the given object into new entities.\n     */\n    create(\n        plainEntityLikeOrPlainEntityLikes?:\n            | DeepPartial<Entity>\n            | DeepPartial<Entity>[],\n    ): Entity | Entity[] {\n        return this.manager.create(\n            this.metadata.target as any,\n            plainEntityLikeOrPlainEntityLikes as any,\n        )\n    }\n\n    /**\n     * Merges multiple entities (or entity-like objects) into a given entity.\n     */\n    merge(\n        mergeIntoEntity: Entity,\n        ...entityLikes: DeepPartial<Entity>[]\n    ): Entity {\n        return this.manager.merge(\n            this.metadata.target as any,\n            mergeIntoEntity,\n            ...entityLikes,\n        )\n    }\n\n    /**\n     * Creates a new entity from the given plain javascript object. If entity already exist in the database, then\n     * it loads it (and everything related to it), replaces all values with the new ones from the given object\n     * and returns this new entity. This new entity is actually a loaded from the db entity with all properties\n     * replaced from the new object.\n     *\n     * Note that given entity-like object must have an entity id / primary key to find entity by.\n     * Returns undefined if entity with given id was not found.\n     */\n    preload(entityLike: DeepPartial<Entity>): Promise<Entity | undefined> {\n        return this.manager.preload(this.metadata.target as any, entityLike)\n    }\n\n    /**\n     * Saves all given entities in the database.\n     * If entities do not exist in the database then inserts, otherwise updates.\n     */\n    save<T extends DeepPartial<Entity>>(\n        entities: T[],\n        options: SaveOptions & { reload: false },\n    ): Promise<T[]>\n\n    /**\n     * Saves all given entities in the database.\n     * If entities do not exist in the database then inserts, otherwise updates.\n     */\n    save<T extends DeepPartial<Entity>>(\n        entities: T[],\n        options?: SaveOptions,\n    ): Promise<(T & Entity)[]>\n\n    /**\n     * Saves a given entity in the database.\n     * If entity does not exist in the database then inserts, otherwise updates.\n     */\n    save<T extends DeepPartial<Entity>>(\n        entity: T,\n        options: SaveOptions & { reload: false },\n    ): Promise<T>\n\n    /**\n     * Saves a given entity in the database.\n     * If entity does not exist in the database then inserts, otherwise updates.\n     */\n    save<T extends DeepPartial<Entity>>(\n        entity: T,\n        options?: SaveOptions,\n    ): Promise<T & Entity>\n\n    /**\n     * Saves one or many given entities.\n     */\n    save<T extends DeepPartial<Entity>>(\n        entityOrEntities: T | T[],\n        options?: SaveOptions,\n    ): Promise<T | T[]> {\n        return this.manager.save<Entity, T>(\n            this.metadata.target as any,\n            entityOrEntities as any,\n            options,\n        )\n    }\n\n    /**\n     * Removes a given entities from the database.\n     */\n    remove(entities: Entity[], options?: RemoveOptions): Promise<Entity[]>\n\n    /**\n     * Removes a given entity from the database.\n     */\n    remove(entity: Entity, options?: RemoveOptions): Promise<Entity>\n\n    /**\n     * Removes one or many given entities.\n     */\n    remove(\n        entityOrEntities: Entity | Entity[],\n        options?: RemoveOptions,\n    ): Promise<Entity | Entity[]> {\n        return this.manager.remove(\n            this.metadata.target as any,\n            entityOrEntities as any,\n            options,\n        )\n    }\n\n    /**\n     * Records the delete date of all given entities.\n     */\n    softRemove<T extends DeepPartial<Entity>>(\n        entities: T[],\n        options: SaveOptions & { reload: false },\n    ): Promise<T[]>\n\n    /**\n     * Records the delete date of all given entities.\n     */\n    softRemove<T extends DeepPartial<Entity>>(\n        entities: T[],\n        options?: SaveOptions,\n    ): Promise<(T & Entity)[]>\n\n    /**\n     * Records the delete date of a given entity.\n     */\n    softRemove<T extends DeepPartial<Entity>>(\n        entity: T,\n        options: SaveOptions & { reload: false },\n    ): Promise<T>\n\n    /**\n     * Records the delete date of a given entity.\n     */\n    softRemove<T extends DeepPartial<Entity>>(\n        entity: T,\n        options?: SaveOptions,\n    ): Promise<T & Entity>\n\n    /**\n     * Records the delete date of one or many given entities.\n     */\n    softRemove<T extends DeepPartial<Entity>>(\n        entityOrEntities: T | T[],\n        options?: SaveOptions,\n    ): Promise<T | T[]> {\n        return this.manager.softRemove<Entity, T>(\n            this.metadata.target as any,\n            entityOrEntities as any,\n            options,\n        )\n    }\n\n    /**\n     * Recovers all given entities in the database.\n     */\n    recover<T extends DeepPartial<Entity>>(\n        entities: T[],\n        options: SaveOptions & { reload: false },\n    ): Promise<T[]>\n\n    /**\n     * Recovers all given entities in the database.\n     */\n    recover<T extends DeepPartial<Entity>>(\n        entities: T[],\n        options?: SaveOptions,\n    ): Promise<(T & Entity)[]>\n\n    /**\n     * Recovers a given entity in the database.\n     */\n    recover<T extends DeepPartial<Entity>>(\n        entity: T,\n        options: SaveOptions & { reload: false },\n    ): Promise<T>\n\n    /**\n     * Recovers a given entity in the database.\n     */\n    recover<T extends DeepPartial<Entity>>(\n        entity: T,\n        options?: SaveOptions,\n    ): Promise<T & Entity>\n\n    /**\n     * Recovers one or many given entities.\n     */\n    recover<T extends DeepPartial<Entity>>(\n        entityOrEntities: T | T[],\n        options?: SaveOptions,\n    ): Promise<T | T[]> {\n        return this.manager.recover<Entity, T>(\n            this.metadata.target as any,\n            entityOrEntities as any,\n            options,\n        )\n    }\n\n    /**\n     * Inserts a given entity into the database.\n     * Unlike save method executes a primitive operation without cascades, relations and other operations included.\n     * Executes fast and efficient INSERT query.\n     * Does not check if entity exist in the database, so query will fail if duplicate entity is being inserted.\n     */\n    insert(\n        entity:\n            | QueryDeepPartialEntity<Entity>\n            | QueryDeepPartialEntity<Entity>[],\n    ): Promise<InsertResult> {\n        return this.manager.insert(this.metadata.target as any, entity)\n    }\n\n    /**\n     * Updates entity partially. Entity can be found by a given conditions.\n     * Unlike save method executes a primitive operation without cascades, relations and other operations included.\n     * Executes fast and efficient UPDATE query.\n     * Does not check if entity exist in the database.\n     */\n    update(\n        criteria:\n            | string\n            | string[]\n            | number\n            | number[]\n            | Date\n            | Date[]\n            | ObjectId\n            | ObjectId[]\n            | FindOptionsWhere<Entity>\n            | FindOptionsWhere<Entity>[],\n        partialEntity: QueryDeepPartialEntity<Entity>,\n    ): Promise<UpdateResult> {\n        return this.manager.update(\n            this.metadata.target,\n            criteria,\n            partialEntity,\n        )\n    }\n\n    /**\n     * Updates all entities of target type, setting fields from supplied partial entity.\n     * This is a primitive operation without cascades, relations or other operations included.\n     * Executes fast and efficient UPDATE query without WHERE clause.\n     *\n     * WARNING! This method updates ALL rows in the target table.\n     */\n    updateAll(\n        partialEntity: QueryDeepPartialEntity<Entity>,\n    ): Promise<UpdateResult> {\n        return this.manager.updateAll(this.metadata.target, partialEntity)\n    }\n\n    /**\n     * Inserts a given entity into the database, unless a unique constraint conflicts then updates the entity\n     * Unlike save method executes a primitive operation without cascades, relations and other operations included.\n     * Executes fast and efficient INSERT ... ON CONFLICT DO UPDATE/ON DUPLICATE KEY UPDATE query.\n     */\n    upsert(\n        entityOrEntities:\n            | QueryDeepPartialEntity<Entity>\n            | QueryDeepPartialEntity<Entity>[],\n        conflictPathsOrOptions: string[] | UpsertOptions<Entity>,\n    ): Promise<InsertResult> {\n        return this.manager.upsert(\n            this.metadata.target as any,\n            entityOrEntities,\n            conflictPathsOrOptions,\n        )\n    }\n\n    /**\n     * Deletes entities by a given criteria.\n     * Unlike save method executes a primitive operation without cascades, relations and other operations included.\n     * Executes fast and efficient DELETE query.\n     * Does not check if entity exist in the database.\n     */\n    delete(\n        criteria:\n            | string\n            | string[]\n            | number\n            | number[]\n            | Date\n            | Date[]\n            | ObjectId\n            | ObjectId[]\n            | FindOptionsWhere<Entity>\n            | FindOptionsWhere<Entity>[],\n    ): Promise<DeleteResult> {\n        return this.manager.delete(this.metadata.target, criteria)\n    }\n\n    /**\n     * Deletes all entities of target type.\n     * This is a primitive operation without cascades, relations or other operations included.\n     * Executes fast and efficient DELETE query without WHERE clause.\n     *\n     * WARNING! This method deletes ALL rows in the target table.\n     */\n    deleteAll(): Promise<DeleteResult> {\n        return this.manager.deleteAll(this.metadata.target)\n    }\n\n    /**\n     * Records the delete date of entities by a given criteria.\n     * Unlike save method executes a primitive operation without cascades, relations and other operations included.\n     * Executes fast and efficient UPDATE query.\n     * Does not check if entity exist in the database.\n     */\n    softDelete(\n        criteria:\n            | string\n            | string[]\n            | number\n            | number[]\n            | Date\n            | Date[]\n            | ObjectId\n            | ObjectId[]\n            | FindOptionsWhere<Entity>\n            | FindOptionsWhere<Entity>[],\n    ): Promise<UpdateResult> {\n        return this.manager.softDelete(\n            this.metadata.target as any,\n            criteria as any,\n        )\n    }\n\n    /**\n     * Restores entities by a given criteria.\n     * Unlike save method executes a primitive operation without cascades, relations and other operations included.\n     * Executes fast and efficient UPDATE query.\n     * Does not check if entity exist in the database.\n     */\n    restore(\n        criteria:\n            | string\n            | string[]\n            | number\n            | number[]\n            | Date\n            | Date[]\n            | ObjectId\n            | ObjectId[]\n            | FindOptionsWhere<Entity>\n            | FindOptionsWhere<Entity>[],\n    ): Promise<UpdateResult> {\n        return this.manager.restore(\n            this.metadata.target as any,\n            criteria as any,\n        )\n    }\n\n    /**\n     * Checks whether any entity exists that matches the given options.\n     *\n     * @deprecated use `exists` method instead, for example:\n     *\n     * .exists()\n     */\n    exist(options?: FindManyOptions<Entity>): Promise<boolean> {\n        return this.manager.exists(this.metadata.target, options)\n    }\n\n    /**\n     * Checks whether any entity exists that matches the given options.\n     */\n    exists(options?: FindManyOptions<Entity>): Promise<boolean> {\n        return this.manager.exists(this.metadata.target, options)\n    }\n\n    /**\n     * Checks whether any entity exists that matches the given conditions.\n     */\n    existsBy(\n        where: FindOptionsWhere<Entity> | FindOptionsWhere<Entity>[],\n    ): Promise<boolean> {\n        return this.manager.existsBy(this.metadata.target, where)\n    }\n\n    /**\n     * Counts entities that match given options.\n     * Useful for pagination.\n     */\n    count(options?: FindManyOptions<Entity>): Promise<number> {\n        return this.manager.count(this.metadata.target, options)\n    }\n\n    /**\n     * Counts entities that match given conditions.\n     * Useful for pagination.\n     */\n    countBy(\n        where: FindOptionsWhere<Entity> | FindOptionsWhere<Entity>[],\n    ): Promise<number> {\n        return this.manager.countBy(this.metadata.target, where)\n    }\n\n    /**\n     * Return the SUM of a column\n     */\n    sum(\n        columnName: PickKeysByType<Entity, number>,\n        where?: FindOptionsWhere<Entity> | FindOptionsWhere<Entity>[],\n    ): Promise<number | null> {\n        return this.manager.sum(this.metadata.target, columnName, where)\n    }\n\n    /**\n     * Return the AVG of a column\n     */\n    average(\n        columnName: PickKeysByType<Entity, number>,\n        where?: FindOptionsWhere<Entity> | FindOptionsWhere<Entity>[],\n    ): Promise<number | null> {\n        return this.manager.average(this.metadata.target, columnName, where)\n    }\n\n    /**\n     * Return the MIN of a column\n     */\n    minimum(\n        columnName: PickKeysByType<Entity, number>,\n        where?: FindOptionsWhere<Entity> | FindOptionsWhere<Entity>[],\n    ): Promise<number | null> {\n        return this.manager.minimum(this.metadata.target, columnName, where)\n    }\n\n    /**\n     * Return the MAX of a column\n     */\n    maximum(\n        columnName: PickKeysByType<Entity, number>,\n        where?: FindOptionsWhere<Entity> | FindOptionsWhere<Entity>[],\n    ): Promise<number | null> {\n        return this.manager.maximum(this.metadata.target, columnName, where)\n    }\n\n    /**\n     * Finds entities that match given find options.\n     */\n    async find(options?: FindManyOptions<Entity>): Promise<Entity[]> {\n        return this.manager.find(this.metadata.target, options)\n    }\n\n    /**\n     * Finds entities that match given find options.\n     */\n    async findBy(\n        where: FindOptionsWhere<Entity> | FindOptionsWhere<Entity>[],\n    ): Promise<Entity[]> {\n        return this.manager.findBy(this.metadata.target, where)\n    }\n\n    /**\n     * Finds entities that match given find options.\n     * Also counts all entities that match given conditions,\n     * but ignores pagination settings (from and take options).\n     */\n    findAndCount(\n        options?: FindManyOptions<Entity>,\n    ): Promise<[Entity[], number]> {\n        return this.manager.findAndCount(this.metadata.target, options)\n    }\n\n    /**\n     * Finds entities that match given WHERE conditions.\n     * Also counts all entities that match given conditions,\n     * but ignores pagination settings (from and take options).\n     */\n    findAndCountBy(\n        where: FindOptionsWhere<Entity> | FindOptionsWhere<Entity>[],\n    ): Promise<[Entity[], number]> {\n        return this.manager.findAndCountBy(this.metadata.target, where)\n    }\n\n    /**\n     * Finds entities with ids.\n     * Optionally find options or conditions can be applied.\n     *\n     * @deprecated use `findBy` method instead in conjunction with `In` operator, for example:\n     *\n     * .findBy({\n     *     id: In([1, 2, 3])\n     * })\n     */\n    async findByIds(ids: any[]): Promise<Entity[]> {\n        return this.manager.findByIds(this.metadata.target, ids)\n    }\n\n    /**\n     * Finds first entity by a given find options.\n     * If entity was not found in the database - returns null.\n     */\n    async findOne(options: FindOneOptions<Entity>): Promise<Entity | null> {\n        return this.manager.findOne(this.metadata.target, options)\n    }\n\n    /**\n     * Finds first entity that matches given where condition.\n     * If entity was not found in the database - returns null.\n     */\n    async findOneBy(\n        where: FindOptionsWhere<Entity> | FindOptionsWhere<Entity>[],\n    ): Promise<Entity | null> {\n        return this.manager.findOneBy(this.metadata.target, where)\n    }\n\n    /**\n     * Finds first entity that matches given id.\n     * If entity was not found in the database - returns null.\n     *\n     * @deprecated use `findOneBy` method instead in conjunction with `In` operator, for example:\n     *\n     * .findOneBy({\n     *     id: 1 // where \"id\" is your primary column name\n     * })\n     */\n    async findOneById(\n        id: number | string | Date | ObjectId,\n    ): Promise<Entity | null> {\n        return this.manager.findOneById(this.metadata.target, id)\n    }\n\n    /**\n     * Finds first entity by a given find options.\n     * If entity was not found in the database - rejects with error.\n     */\n    async findOneOrFail(options: FindOneOptions<Entity>): Promise<Entity> {\n        return this.manager.findOneOrFail(this.metadata.target, options)\n    }\n\n    /**\n     * Finds first entity that matches given where condition.\n     * If entity was not found in the database - rejects with error.\n     */\n    async findOneByOrFail(\n        where: FindOptionsWhere<Entity> | FindOptionsWhere<Entity>[],\n    ): Promise<Entity> {\n        return this.manager.findOneByOrFail(this.metadata.target, where)\n    }\n\n    /**\n     * Executes a raw SQL query and returns a raw database results.\n     * Raw query execution is supported only by relational databases (MongoDB is not supported).\n     *\n     * @see [Official docs](https://typeorm.io/repository-api) for examples.\n     */\n    query<T = any>(query: string, parameters?: any[]): Promise<T> {\n        return this.manager.query(query, parameters)\n    }\n\n    /**\n     * Tagged template function that executes raw SQL query and returns raw database results.\n     * Template expressions are automatically transformed into database parameters.\n     * Raw query execution is supported only by relational databases (MongoDB is not supported).\n     * Note: Don't call this as a regular function, it is meant to be used with backticks to tag a template literal.\n     * Example: repository.sql`SELECT * FROM table_name WHERE id = ${id}`\n     */\n    async sql<T = any>(\n        strings: TemplateStringsArray,\n        ...values: unknown[]\n    ): Promise<T> {\n        const { query, parameters } = buildSqlTag({\n            driver: this.manager.connection.driver,\n            strings: strings,\n            expressions: values,\n        })\n\n        return await this.query(query, parameters)\n    }\n\n    /**\n     * Clears all the data from the given table/collection (truncates/drops it).\n     *\n     * Note: this method uses TRUNCATE and may not work as you expect in transactions on some platforms.\n     * @see https://stackoverflow.com/a/5972738/925151\n     */\n    clear(): Promise<void> {\n        return this.manager.clear(this.metadata.target)\n    }\n\n    /**\n     * Increments some column by provided value of the entities matched given conditions.\n     */\n    increment(\n        conditions: FindOptionsWhere<Entity>,\n        propertyPath: string,\n        value: number | string,\n    ): Promise<UpdateResult> {\n        return this.manager.increment(\n            this.metadata.target,\n            conditions,\n            propertyPath,\n            value,\n        )\n    }\n\n    /**\n     * Decrements some column by provided value of the entities matched given conditions.\n     */\n    decrement(\n        conditions: FindOptionsWhere<Entity>,\n        propertyPath: string,\n        value: number | string,\n    ): Promise<UpdateResult> {\n        return this.manager.decrement(\n            this.metadata.target,\n            conditions,\n            propertyPath,\n            value,\n        )\n    }\n\n    /**\n     * Extends repository with provided functions.\n     */\n    extend<CustomRepository>(\n        customs: CustomRepository & ThisType<this & CustomRepository>,\n    ): this & CustomRepository {\n        // return {\n        //     ...this,\n        //     ...custom\n        // };\n        const thisRepo: any = this.constructor\n        const { target, manager, queryRunner } = this\n        const ChildClass = class extends thisRepo {\n            constructor(\n                target: EntityTarget<Entity>,\n                manager: EntityManager,\n                queryRunner?: QueryRunner,\n            ) {\n                super(target, manager, queryRunner)\n            }\n        }\n        for (const custom in customs)\n            ChildClass.prototype[custom] = customs[custom]\n        return new ChildClass(target, manager, queryRunner) as any\n    }\n}\n"], "sourceRoot": ".."}