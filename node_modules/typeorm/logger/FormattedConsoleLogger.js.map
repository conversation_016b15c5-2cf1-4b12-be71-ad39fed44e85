{"version": 3, "sources": ["../../src/logger/FormattedConsoleLogger.ts"], "names": [], "mappings": ";;;AAAA,6DAAyD;AACzD,qDAAiD;AAIjD;;;GAGG;AACH,MAAa,sBAAuB,SAAQ,+BAAc;IACtD;;OAEG;IACO,QAAQ,CACd,KAAe,EACf,UAAqC,EACrC,WAAyB;QAEzB,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CACpC,UAAU,EACV;YACI,YAAY,EAAE,IAAI;YAClB,SAAS,EAAE,IAAI;SAClB,EACD,WAAW,CACd,CAAA;QAED,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC7B,QAAQ,OAAO,CAAC,IAAI,IAAI,KAAK,EAAE,CAAC;gBAC5B,KAAK,KAAK,CAAC;gBACX,KAAK,cAAc,CAAC;gBACpB,KAAK,WAAW;oBACZ,6BAAa,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAA;oBAC1C,MAAK;gBAET,KAAK,MAAM,CAAC;gBACZ,KAAK,OAAO;oBACR,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;wBACjB,6BAAa,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,CAAA;oBAC1D,CAAC;yBAAM,CAAC;wBACJ,6BAAa,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAA;oBAC9C,CAAC;oBACD,MAAK;gBAET,KAAK,MAAM,CAAC;gBACZ,KAAK,YAAY;oBACb,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;wBACjB,6BAAa,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,CAAA;oBAC1D,CAAC;yBAAM,CAAC;wBACJ,OAAO,CAAC,IAAI,CACR,6BAAa,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAC9C,CAAA;oBACL,CAAC;oBACD,MAAK;gBAET,KAAK,OAAO,CAAC;gBACb,KAAK,aAAa;oBACd,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;wBACjB,6BAAa,CAAC,QAAQ,CAClB,OAAO,CAAC,MAAM,EACd,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAC1B,CAAA;oBACL,CAAC;yBAAM,CAAC;wBACJ,OAAO,CAAC,KAAK,CACT,6BAAa,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAC/C,CAAA;oBACL,CAAC;oBACD,MAAK;YACb,CAAC;QACL,CAAC;IACL,CAAC;CACJ;AA9DD,wDA8DC", "file": "FormattedConsoleLogger.js", "sourcesContent": ["import { PlatformTools } from \"../platform/PlatformTools\"\nimport { <PERSON>bstractLogger } from \"./AbstractLogger\"\nimport { LogLevel, LogMessage } from \"./Logger\"\nimport { QueryRunner } from \"../query-runner/QueryRunner\"\n\n/**\n * Performs logging of the events in TypeORM.\n * This version of logger uses console to log events, syntax highlighting and formatting.\n */\nexport class FormattedConsoleLogger extends AbstractLogger {\n    /**\n     * Write log to specific output.\n     */\n    protected writeLog(\n        level: LogLevel,\n        logMessage: LogMessage | LogMessage[],\n        queryRunner?: QueryRunner,\n    ) {\n        const messages = this.prepareLogMessages(\n            logMessage,\n            {\n                highlightSql: true,\n                formatSql: true,\n            },\n            queryRunner,\n        )\n\n        for (const message of messages) {\n            switch (message.type ?? level) {\n                case \"log\":\n                case \"schema-build\":\n                case \"migration\":\n                    PlatformTools.log(String(message.message))\n                    break\n\n                case \"info\":\n                case \"query\":\n                    if (message.prefix) {\n                        PlatformTools.logInfo(message.prefix, message.message)\n                    } else {\n                        PlatformTools.log(String(message.message))\n                    }\n                    break\n\n                case \"warn\":\n                case \"query-slow\":\n                    if (message.prefix) {\n                        PlatformTools.logWarn(message.prefix, message.message)\n                    } else {\n                        console.warn(\n                            PlatformTools.warn(String(message.message)),\n                        )\n                    }\n                    break\n\n                case \"error\":\n                case \"query-error\":\n                    if (message.prefix) {\n                        PlatformTools.logError(\n                            message.prefix,\n                            String(message.message),\n                        )\n                    } else {\n                        console.error(\n                            PlatformTools.error(String(message.message)),\n                        )\n                    }\n                    break\n            }\n        }\n    }\n}\n"], "sourceRoot": ".."}