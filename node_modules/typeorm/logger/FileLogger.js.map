{"version": 3, "sources": ["../../src/logger/FileLogger.ts"], "names": [], "mappings": ";;;;AAEA,0EAAuC;AAEvC,6DAAyD;AACzD,qDAAiD;AAEjD;;;GAGG;AACH,MAAa,UAAW,SAAQ,+BAAc;IAC1C,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YACI,OAAuB,EACf,iBAAqC;QAE7C,KAAK,CAAC,OAAO,CAAC,CAAA;QAFN,sBAAiB,GAAjB,iBAAiB,CAAoB;IAGjD,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;OAEG;IACO,QAAQ,CACd,KAAe,EACf,UAAqC,EACrC,WAAyB;QAEzB,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE;YACjD,YAAY,EAAE,KAAK;YACnB,gBAAgB,EAAE,KAAK;SAC1B,CAAC,CAAA;QAEF,MAAM,OAAO,GAAa,EAAE,CAAA;QAE5B,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC7B,QAAQ,OAAO,CAAC,IAAI,IAAI,KAAK,EAAE,CAAC;gBAC5B,KAAK,KAAK;oBACN,OAAO,CAAC,IAAI,CAAC,UAAU,OAAO,CAAC,OAAO,EAAE,CAAC,CAAA;oBACzC,MAAK;gBAET,KAAK,cAAc,CAAC;gBACpB,KAAK,WAAW;oBACZ,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAA;oBACrC,MAAK;gBAET,KAAK,MAAM;oBACP,OAAO,CAAC,IAAI,CAAC,WAAW,OAAO,CAAC,OAAO,EAAE,CAAC,CAAA;oBAC1C,MAAK;gBAET,KAAK,OAAO;oBACR,OAAO,CAAC,IAAI,CAAC,YAAY,OAAO,CAAC,OAAO,EAAE,CAAC,CAAA;oBAC3C,MAAK;gBAET,KAAK,MAAM;oBACP,OAAO,CAAC,IAAI,CAAC,WAAW,OAAO,CAAC,OAAO,EAAE,CAAC,CAAA;oBAC1C,MAAK;gBAET,KAAK,YAAY;oBACb,IAAI,OAAO,CAAC,MAAM,KAAK,gBAAgB,EAAE,CAAC;wBACtC,SAAQ;oBACZ,CAAC;oBAED,IAAI,CAAC,KAAK,CACN,gBAAgB,OAAO,CAAC,cAAc,EAAE,IAAI,SAAS,OAAO,CAAC,OAAO,EAAE,CACzE,CAAA;oBACD,MAAK;gBAET,KAAK,OAAO,CAAC;gBACb,KAAK,aAAa;oBACd,IAAI,OAAO,CAAC,MAAM,KAAK,cAAc,EAAE,CAAC;wBACpC,OAAO,CAAC,IAAI,CAAC,mBAAmB,OAAO,CAAC,OAAO,EAAE,CAAC,CAAA;oBACtD,CAAC;yBAAM,IAAI,OAAO,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;wBACxC,OAAO,CAAC,IAAI,CAAC,kBAAkB,OAAO,CAAC,OAAO,EAAE,CAAC,CAAA;oBACrD,CAAC;yBAAM,CAAC;wBACJ,OAAO,CAAC,IAAI,CAAC,YAAY,OAAO,CAAC,OAAO,EAAE,CAAC,CAAA;oBAC/C,CAAC;oBACD,MAAK;YACb,CAAC;QACL,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;IACvB,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,OAA0B;QACtC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAA;QACtD,MAAM,QAAQ,GAAG,uBAAW,CAAC,IAAI,GAAG,GAAG,CAAA;QACvC,IAAI,OAAO,GAAG,aAAa,CAAA;QAC3B,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;YAC3D,OAAO,GAAG,6BAAa,CAAC,aAAa,CACjC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CACjC,CAAA;QACL,CAAC;QACD,OAAO,GAAI,OAAoB,CAAC,GAAG,CAC/B,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,GAAG,GAAG,GAAG,GAAG,CACtD,CAAA;QACD,6BAAa,CAAC,cAAc,CACxB,QAAQ,GAAG,OAAO,EAClB,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM,CAChC,CAAA,CAAC,yCAAyC;IAC/C,CAAC;CACJ;AApGD,gCAoGC", "file": "FileLogger.js", "sourcesContent": ["import { FileLoggerOptions, LoggerOptions } from \"./LoggerOptions\"\nimport { LogLevel, LogMessage } from \"./Logger\"\nimport appRootPath from \"app-root-path\"\nimport { QueryRunner } from \"../query-runner/QueryRunner\"\nimport { PlatformTools } from \"../platform/PlatformTools\"\nimport { AbstractLogger } from \"./AbstractLogger\"\n\n/**\n * Performs logging of the events in TypeORM.\n * This version of logger logs everything into ormlogs.log file.\n */\nexport class FileLogger extends AbstractLogger {\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(\n        options?: LoggerOptions,\n        private fileLoggerOptions?: FileLoggerOptions,\n    ) {\n        super(options)\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Write log to specific output.\n     */\n    protected writeLog(\n        level: LogLevel,\n        logMessage: LogMessage | LogMessage[],\n        queryRunner?: QueryRunner,\n    ) {\n        const messages = this.prepareLogMessages(logMessage, {\n            highlightSql: false,\n            addColonToPrefix: false,\n        })\n\n        const strings: string[] = []\n\n        for (const message of messages) {\n            switch (message.type ?? level) {\n                case \"log\":\n                    strings.push(`[LOG]: ${message.message}`)\n                    break\n\n                case \"schema-build\":\n                case \"migration\":\n                    strings.push(String(message.message))\n                    break\n\n                case \"info\":\n                    strings.push(`[INFO]: ${message.message}`)\n                    break\n\n                case \"query\":\n                    strings.push(`[QUERY]: ${message.message}`)\n                    break\n\n                case \"warn\":\n                    strings.push(`[WARN]: ${message.message}`)\n                    break\n\n                case \"query-slow\":\n                    if (message.prefix === \"execution time\") {\n                        continue\n                    }\n\n                    this.write(\n                        `[SLOW QUERY: ${message.additionalInfo?.time} ms]: ${message.message}`,\n                    )\n                    break\n\n                case \"error\":\n                case \"query-error\":\n                    if (message.prefix === \"query failed\") {\n                        strings.push(`[FAILED QUERY]: ${message.message}`)\n                    } else if (message.type === \"query-error\") {\n                        strings.push(`[QUERY ERROR]: ${message.message}`)\n                    } else {\n                        strings.push(`[ERROR]: ${message.message}`)\n                    }\n                    break\n            }\n        }\n\n        this.write(strings)\n    }\n\n    /**\n     * Writes given strings into the log file.\n     */\n    protected write(strings: string | string[]) {\n        strings = Array.isArray(strings) ? strings : [strings]\n        const basePath = appRootPath.path + \"/\"\n        let logPath = \"ormlogs.log\"\n        if (this.fileLoggerOptions && this.fileLoggerOptions.logPath) {\n            logPath = PlatformTools.pathNormalize(\n                this.fileLoggerOptions.logPath,\n            )\n        }\n        strings = (strings as string[]).map(\n            (str) => \"[\" + new Date().toISOString() + \"]\" + str,\n        )\n        PlatformTools.appendFileSync(\n            basePath + logPath,\n            strings.join(\"\\r\\n\") + \"\\r\\n\",\n        ) // todo: use async or implement promises?\n    }\n}\n"], "sourceRoot": ".."}