"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NoVersionOrUpdateDateColumnError = void 0;
const TypeORMError_1 = require("./TypeORMError");
/**
 * Thrown when an entity does not have no version and no update date column.
 */
class NoVersionOrUpdateDateColumnError extends TypeORMError_1.TypeORMError {
    constructor(entity) {
        super(`Entity ${entity} does not have version or update date columns.`);
    }
}
exports.NoVersionOrUpdateDateColumnError = NoVersionOrUpdateDateColumnError;

//# sourceMappingURL=NoVersionOrUpdateDateColumnError.js.map
