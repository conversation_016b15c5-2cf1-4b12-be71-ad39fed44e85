"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PessimisticLockTransactionRequiredError = void 0;
const TypeORMError_1 = require("./TypeORMError");
/**
 * Thrown when a transaction is required for the current operation, but there is none open.
 */
class PessimisticLockTransactionRequiredError extends TypeORMError_1.TypeORMError {
    constructor() {
        super(`An open transaction is required for pessimistic lock.`);
    }
}
exports.PessimisticLockTransactionRequiredError = PessimisticLockTransactionRequiredError;

//# sourceMappingURL=PessimisticLockTransactionRequiredError.js.map
