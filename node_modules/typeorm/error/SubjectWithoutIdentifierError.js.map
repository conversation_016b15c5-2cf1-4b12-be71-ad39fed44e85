{"version": 3, "sources": ["../../src/error/SubjectWithoutIdentifierError.ts"], "names": [], "mappings": ";;;AACA,iDAA6C;AAE7C;;;;GAIG;AACH,MAAa,6BAA8B,SAAQ,2BAAY;IAC3D,YAAY,OAAgB;QACxB,KAAK,CACD,2BAA2B,OAAO,CAAC,QAAQ,CAAC,UAAU,gDAAgD,CACzG,CAAA;IACL,CAAC;CACJ;AAND,sEAMC", "file": "SubjectWithoutIdentifierError.js", "sourcesContent": ["import { Subject } from \"../persistence/Subject\"\nimport { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when operation is going to be executed on a subject without identifier.\n * This error should never be thrown, however it still presents to prevent user from updation or removing the whole table.\n * If this error occurs still, it most probably is an ORM internal problem which must be reported and fixed.\n */\nexport class SubjectWithoutIdentifierError extends TypeORMError {\n    constructor(subject: Subject) {\n        super(\n            `Internal error. Subject ${subject.metadata.targetName} must have an identifier to perform operation.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}