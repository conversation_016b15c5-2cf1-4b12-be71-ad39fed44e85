"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PersistedEntityNotFoundError = void 0;
const TypeORMError_1 = require("./TypeORMError");
/**
 * Thrown . Theoretically can't be thrown.
 */
class PersistedEntityNotFoundError extends TypeORMError_1.TypeORMError {
    constructor() {
        super(`Internal error. Persisted entity was not found in the list of prepared operated entities.`);
    }
}
exports.PersistedEntityNotFoundError = PersistedEntityNotFoundError;

//# sourceMappingURL=PersistedEntityNotFoundError.js.map
