{"version": 3, "sources": ["../../src/find-options/operator/LessThanOrEqual.ts"], "names": [], "mappings": ";;AAMA,0CAEC;AARD,kDAA8C;AAE9C;;;GAGG;AACH,SAAgB,eAAe,CAAI,KAA0B;IACzD,OAAO,IAAI,2BAAY,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAA;AACrD,CAAC", "file": "LessThanOrEqual.js", "sourcesContent": ["import { FindOperator } from \"../FindOperator\"\n\n/**\n * Find Options Operator.\n * Example: { someField: LessThanOrEqual(10) }\n */\nexport function LessThanOrEqual<T>(value: T | FindOperator<T>) {\n    return new FindOperator(\"lessThanOrEqual\", value)\n}\n"], "sourceRoot": "../.."}