{"version": 3, "sources": ["../../src/find-options/operator/ArrayContains.ts"], "names": [], "mappings": ";;AAMA,sCAIC;AAVD,kDAA8C;AAE9C;;;GAGG;AACH,SAAgB,aAAa,CACzB,KAAqC;IAErC,OAAO,IAAI,2BAAY,CAAC,eAAe,EAAE,KAAY,CAAC,CAAA;AAC1D,CAAC", "file": "ArrayContains.js", "sourcesContent": ["import { FindOperator } from \"../FindOperator\"\n\n/**\n * FindOptions Operator.\n * Example: { someField: ArrayContains([...]) }\n */\nexport function ArrayContains<T>(\n    value: readonly T[] | FindOperator<T>,\n): FindOperator<any> {\n    return new FindOperator(\"arrayContains\", value as any)\n}\n"], "sourceRoot": "../.."}