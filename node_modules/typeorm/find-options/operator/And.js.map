{"version": 3, "sources": ["../../src/find-options/operator/And.ts"], "names": [], "mappings": ";;AAEA,kBAEC;AAJD,kDAA8C;AAE9C,SAAgB,GAAG,CAAI,GAAG,MAAyB;IAC/C,OAAO,IAAI,2BAAY,CAAC,KAAK,EAAE,MAAa,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;AAC7D,CAAC", "file": "And.js", "sourcesContent": ["import { FindOperator } from \"../FindOperator\"\n\nexport function And<T>(...values: FindOperator<T>[]): FindOperator<T> {\n    return new FindOperator(\"and\", values as any, true, true)\n}\n"], "sourceRoot": "../.."}