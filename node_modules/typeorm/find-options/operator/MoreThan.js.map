{"version": 3, "sources": ["../../src/find-options/operator/MoreThan.ts"], "names": [], "mappings": ";;AAMA,4BAEC;AARD,kDAA8C;AAE9C;;;GAGG;AACH,SAAgB,QAAQ,CAAI,KAA0B;IAClD,OAAO,IAAI,2BAAY,CAAC,UAAU,EAAE,KAAK,CAAC,CAAA;AAC9C,CAAC", "file": "MoreThan.js", "sourcesContent": ["import { FindOperator } from \"../FindOperator\"\n\n/**\n * Find Options Operator.\n * Example: { someField: MoreThan(10) }\n */\nexport function MoreThan<T>(value: T | FindOperator<T>) {\n    return new FindOperator(\"moreThan\", value)\n}\n"], "sourceRoot": "../.."}