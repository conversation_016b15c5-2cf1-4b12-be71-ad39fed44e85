{"version": 3, "sources": ["../../src/find-options/operator/Like.ts"], "names": [], "mappings": ";;AAMA,oBAEC;AARD,kDAA8C;AAE9C;;;GAGG;AACH,SAAgB,IAAI,CAAI,KAA0B;IAC9C,OAAO,IAAI,2BAAY,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;AAC1C,CAAC", "file": "Like.js", "sourcesContent": ["import { FindOperator } from \"../FindOperator\"\n\n/**\n * Find Options Operator.\n * Example: { someField: Like(\"%some string%\") }\n */\nexport function Like<T>(value: T | FindOperator<T>) {\n    return new FindOperator(\"like\", value)\n}\n"], "sourceRoot": "../.."}