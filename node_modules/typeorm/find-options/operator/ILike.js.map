{"version": 3, "sources": ["../../src/find-options/operator/ILike.ts"], "names": [], "mappings": ";;AAMA,sBAEC;AARD,kDAA8C;AAE9C;;;GAGG;AACH,SAAgB,KAAK,CAAI,KAA0B;IAC/C,OAAO,IAAI,2BAAY,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;AAC3C,CAAC", "file": "ILike.js", "sourcesContent": ["import { FindOperator } from \"../FindOperator\"\n\n/**\n * Find Options Operator.\n * Example: { someField: ILike(\"%SOME string%\") }\n */\nexport function ILike<T>(value: T | FindOperator<T>) {\n    return new FindOperator(\"ilike\", value)\n}\n"], "sourceRoot": "../.."}