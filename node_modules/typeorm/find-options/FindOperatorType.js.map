{"version": 3, "sources": ["../../src/find-options/FindOperatorType.ts"], "names": [], "mappings": "", "file": "FindOperatorType.js", "sourcesContent": ["/**\n * List of types that FindOperator can be.\n */\nexport type FindOperatorType =\n    | \"not\"\n    | \"lessThan\"\n    | \"lessThanOrEqual\"\n    | \"moreThan\"\n    | \"moreThanOrEqual\"\n    | \"equal\"\n    | \"between\"\n    | \"in\"\n    | \"any\"\n    | \"isNull\"\n    | \"ilike\"\n    | \"like\"\n    | \"raw\"\n    | \"arrayContains\"\n    | \"arrayContainedBy\"\n    | \"arrayOverlap\"\n    | \"and\"\n    | \"jsonContains\"\n    | \"or\"\n"], "sourceRoot": ".."}