{"version": 3, "sources": ["../../src/find-options/FindOptionsSelect.ts"], "names": [], "mappings": "", "file": "FindOptionsSelect.js", "sourcesContent": ["import { ObjectId } from \"../driver/mongodb/typings\"\n\n/**\n * A single property handler for FindOptionsSelect.\n */\nexport type FindOptionsSelectProperty<Property> = Property extends Promise<\n    infer I\n>\n    ? FindOptionsSelectProperty<I> | boolean\n    : Property extends Array<infer I>\n    ? FindOptionsSelectProperty<I> | boolean\n    : Property extends string\n    ? boolean\n    : Property extends number\n    ? boolean\n    : Property extends boolean\n    ? boolean\n    : Property extends Function\n    ? never\n    : Property extends Buffer\n    ? boolean\n    : Property extends Date\n    ? boolean\n    : Property extends ObjectId\n    ? boolean\n    : Property extends object\n    ? FindOptionsSelect<Property> | boolean\n    : boolean\n\n/**\n * Select find options.\n */\nexport type FindOptionsSelect<Entity> = {\n    [P in keyof Entity]?: P extends \"toString\"\n        ? unknown\n        : FindOptionsSelectProperty<NonNullable<Entity[P]>>\n}\n\n/**\n * Property paths (column names) to be selected by \"find\" defined as string.\n * Old selection mechanism in TypeORM.\n *\n * @deprecated will be removed in the next version, use FindOptionsSelect type notation instead\n */\nexport type FindOptionsSelectByString<Entity> = (keyof Entity)[]\n"], "sourceRoot": ".."}