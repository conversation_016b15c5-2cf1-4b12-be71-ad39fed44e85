{"version": 3, "sources": ["../../src/find-options/FindManyOptions.ts"], "names": [], "mappings": "", "file": "FindManyOptions.js", "sourcesContent": ["import { FindOneOptions } from \"./FindOneOptions\"\n\n/**\n * Defines a special criteria to find specific entities.\n */\nexport interface FindManyOptions<Entity = any> extends FindOneOptions<Entity> {\n    /**\n     * Offset (paginated) where from entities should be taken.\n     */\n    skip?: number\n\n    /**\n     * Limit (paginated) - max number of entities should be taken.\n     */\n    take?: number\n}\n"], "sourceRoot": ".."}