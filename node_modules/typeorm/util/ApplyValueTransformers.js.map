{"version": 3, "sources": ["../../src/util/ApplyValueTransformers.ts"], "names": [], "mappings": ";;;AAEA,MAAa,sBAAsB;IAC/B,MAAM,CAAC,aAAa,CAChB,WAAkD,EAClD,aAAkB;QAElB,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC;YAC7B,MAAM,mBAAmB,GAAG,WAAW,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,CAAA;YACzD,OAAO,mBAAmB,CAAC,MAAM,CAC7B,CAAC,gBAAgB,EAAE,YAAY,EAAE,EAAE;gBAC/B,OAAO,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;YAC9C,CAAC,EACD,aAAa,CAChB,CAAA;QACL,CAAC;QACD,OAAO,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;IAC1C,CAAC;IACD,MAAM,CAAC,WAAW,CACd,WAAkD,EAClD,WAAgB;QAEhB,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC;YAC7B,OAAO,WAAW,CAAC,MAAM,CAAC,CAAC,gBAAgB,EAAE,YAAY,EAAE,EAAE;gBACzD,OAAO,YAAY,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAA;YAC5C,CAAC,EAAE,WAAW,CAAC,CAAA;QACnB,CAAC;QACD,OAAO,WAAW,CAAC,EAAE,CAAC,WAAW,CAAC,CAAA;IACtC,CAAC;CACJ;AA3BD,wDA2BC", "file": "ApplyValueTransformers.js", "sourcesContent": ["import { ValueTransformer } from \"../decorator/options/ValueTransformer\"\n\nexport class ApplyValueTransformers {\n    static transformFrom(\n        transformer: ValueTransformer | ValueTransformer[],\n        databaseValue: any,\n    ) {\n        if (Array.isArray(transformer)) {\n            const reverseTransformers = transformer.slice().reverse()\n            return reverseTransformers.reduce(\n                (transformedValue, _transformer) => {\n                    return _transformer.from(transformedValue)\n                },\n                databaseValue,\n            )\n        }\n        return transformer.from(databaseValue)\n    }\n    static transformTo(\n        transformer: ValueTransformer | ValueTransformer[],\n        entityValue: any,\n    ) {\n        if (Array.isArray(transformer)) {\n            return transformer.reduce((transformedValue, _transformer) => {\n                return _transformer.to(transformedValue)\n            }, entityValue)\n        }\n        return transformer.to(entityValue)\n    }\n}\n"], "sourceRoot": ".."}