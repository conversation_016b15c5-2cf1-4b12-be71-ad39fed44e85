{"version": 3, "sources": ["../../src/util/SqlTagUtils.ts"], "names": [], "mappings": ";;AASA,kCA2DC;;AAnED,4DAA2B;AAQ3B,SAAgB,WAAW,CAAC,EACxB,MAAM,EACN,OAAO,EACP,WAAW,GACK;IAChB,IAAI,KAAK,GAAG,EAAE,CAAA;IACd,MAAM,UAAU,GAAc,EAAE,CAAA;IAChC,IAAI,GAAG,GAAG,CAAC,CAAA;IAEX,KAAK,MAAM,CAAC,aAAa,EAAE,UAAU,CAAC,IAAI,WAAW,CAAC,OAAO,EAAE,EAAE,CAAC;QAC9D,KAAK,IAAI,OAAO,CAAC,aAAa,CAAC,CAAA;QAE/B,IAAI,UAAU,KAAK,IAAI,EAAE,CAAC;YACtB,KAAK,IAAI,MAAM,CAAA;YACf,SAAQ;QACZ,CAAC;QAED,IAAI,OAAO,UAAU,KAAK,UAAU,EAAE,CAAC;YACnC,MAAM,KAAK,GAAG,UAAU,EAAE,CAAA;YAE1B,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBAC5B,KAAK,IAAI,KAAK,CAAA;gBACd,SAAQ;YACZ,CAAC;YAED,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;gBACvB,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACrB,MAAM,IAAI,KAAK,CACX,cAAc,aAAa,wIAAwI,CACtK,CAAA;gBACL,CAAC;gBAED,MAAM,WAAW,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE;oBAC/B,OAAO,MAAM,CAAC,eAAe,CAAC,SAAS,GAAG,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAA;gBAC5D,CAAC,CAAC,CAAA;gBAEF,KAAK,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gBAC/B,UAAU,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAA;gBAEzB,SAAQ;YACZ,CAAC;YAED,MAAM,IAAI,KAAK,CACX,cAAc,aAAa,8EACvB,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,KACrC,4GAA4G,CAC/G,CAAA;QACL,CAAC;QAED,KAAK,IAAI,MAAM,CAAC,eAAe,CAAC,SAAS,GAAG,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAA;QAE1D,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;IAC/B,CAAC;IAED,KAAK,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;IAEpC,KAAK,GAAG,IAAA,gBAAM,EAAC,KAAK,CAAC,CAAA;IAErB,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,CAAA;AAChC,CAAC", "file": "SqlTagUtils.js", "sourcesContent": ["import { Driver } from \"../driver/Driver\"\nimport dedent from \"dedent\"\n\ninterface BuildSqlTagParams {\n    driver: Driver\n    strings: TemplateStringsArray\n    expressions: unknown[]\n}\n\nexport function buildSqlTag({\n    driver,\n    strings,\n    expressions,\n}: BuildSqlTagParams): { query: string; parameters: unknown[] } {\n    let query = \"\"\n    const parameters: unknown[] = []\n    let idx = 0\n\n    for (const [expressionIdx, expression] of expressions.entries()) {\n        query += strings[expressionIdx]\n\n        if (expression === null) {\n            query += \"NULL\"\n            continue\n        }\n\n        if (typeof expression === \"function\") {\n            const value = expression()\n\n            if (typeof value === \"string\") {\n                query += value\n                continue\n            }\n\n            if (Array.isArray(value)) {\n                if (value.length === 0) {\n                    throw new Error(\n                        `Expression ${expressionIdx} in this sql tagged template is a function which returned an empty array. Empty arrays cannot safely be expanded into parameter lists.`,\n                    )\n                }\n\n                const arrayParams = value.map(() => {\n                    return driver.createParameter(`param_${idx + 1}`, idx++)\n                })\n\n                query += arrayParams.join(\", \")\n                parameters.push(...value)\n\n                continue\n            }\n\n            throw new Error(\n                `Expression ${expressionIdx} in this sql tagged template is a function which returned a value of type \"${\n                    value === null ? \"null\" : typeof value\n                }\". Only array and string types are supported as function return values in sql tagged template expressions.`,\n            )\n        }\n\n        query += driver.createParameter(`param_${idx + 1}`, idx++)\n\n        parameters.push(expression)\n    }\n\n    query += strings[strings.length - 1]\n\n    query = dedent(query)\n\n    return { query, parameters }\n}\n"], "sourceRoot": ".."}