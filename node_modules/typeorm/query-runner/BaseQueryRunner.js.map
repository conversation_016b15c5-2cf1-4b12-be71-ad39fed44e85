{"version": 3, "sources": ["../../src/query-runner/BaseQueryRunner.ts"], "names": [], "mappings": ";;;AACA,2CAAuC;AACvC,uDAAmD;AAUnD,wDAAoD;AAGpD,+CAA2C;AAE3C,6DAAyD;AACzD,qDAAiD;AAEjD,MAAsB,eAAe;IAArC;QACI,4EAA4E;QAC5E,oBAAoB;QACpB,4EAA4E;QAY5E;;;WAGG;QACH,eAAU,GAAG,KAAK,CAAA;QAElB;;WAEG;QACH,wBAAmB,GAAG,KAAK,CAAA;QAE3B;;;WAGG;QACH,SAAI,GAAG,EAAE,CAAA;QAET;;WAEG;QACH,iBAAY,GAAY,EAAE,CAAA;QAE1B;;WAEG;QACH,gBAAW,GAAW,EAAE,CAAA;QAgBxB;;WAEG;QACO,kBAAa,GAAY,KAAK,CAAA;QAExC;;WAEG;QACO,gBAAW,GAAgB,IAAI,yBAAW,EAAE,CAAA;QAStD;;;WAGG;QACO,qBAAgB,GAAG,CAAC,CAAA;QAEtB,qBAAgB,GAA2B,EAAE,CAAA;IAulBzD,CAAC;IAxkBG;;;;;;OAMG;IACH,KAAK,CAAC,GAAG,CACL,OAA6B,EAC7B,GAAG,MAAiB;QAEpB,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,IAAA,yBAAW,EAAC;YACtC,MAAM,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM;YAC9B,OAAO,EAAE,OAAO;YAChB,WAAW,EAAE,MAAM;SACtB,CAAC,CAAA;QAEF,OAAO,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;IAC9C,CAAC;IAUD,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,KAAK,CAAC,eAAe;QACjB,aAAa;IACjB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc;QAChB,aAAa;IACjB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CAAC,SAAiB;QAC5B,IAAI,CAAC,YAAY,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,CAAC,CAAA;QACtD,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;IAC1E,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CAAC,UAAqB;QACjC,IAAI,CAAC,UAAU,EAAE,CAAC;YACd,4BAA4B;YAC5B,0DAA0D;YAC1D,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAA;QAC5C,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAA;QACrD,OAAO,IAAI,CAAC,YAAY,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO,CAAC,QAAgB;QAC1B,IAAI,CAAC,WAAW,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAA;QACnD,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;IACxE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CAAC,SAAoB;QAC/B,IAAI,CAAC,WAAW,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAA;QAClD,OAAO,IAAI,CAAC,WAAW,CAAA;IAC3B,CAAC;IAED;;;;OAIG;IACH,eAAe;QACX,IAAI,CAAC,WAAW,GAAG,IAAI,yBAAW,EAAE,CAAA;QACpC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAA;IAC7B,CAAC;IAED;;;;;OAKG;IACH,gBAAgB;QACZ,IAAI,CAAC,WAAW,GAAG,IAAI,yBAAW,EAAE,CAAA;QACpC,IAAI,CAAC,aAAa,GAAG,KAAK,CAAA;IAC9B,CAAC;IAED;;OAEG;IACH,cAAc;QACV,IAAI,CAAC,WAAW,GAAG,IAAI,yBAAW,EAAE,CAAA;IACxC,CAAC;IAED;;OAEG;IACH,YAAY;QACR,OAAO,IAAI,CAAC,WAAW,CAAA;IAC3B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB;QACpB,KAAK,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC;YAC7D,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;QACvC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB;QACtB,KAAK,MAAM,EACP,KAAK,EACL,UAAU,GACb,IAAI,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,EAAE,EAAE,CAAC;YAC1C,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;QACvC,CAAC;IACL,CAAC;IAED,kBAAkB;QACd,OAAO,IAAI,CAAC,IAAI,CAAA;IACpB,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;OAEG;IACO,KAAK,CAAC,aAAa,CAAC,QAAgB;QAC1C,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAA;QACpE,IAAI,IAAI;YAAE,OAAO,IAAI,CAAA;QAErB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAA;QACnD,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAA;YACpC,OAAO,UAAU,CAAC,CAAC,CAAC,CAAA;QACxB,CAAC;aAAM,CAAC;YACJ,MAAM,IAAI,2BAAY,CAAC,SAAS,QAAQ,mBAAmB,CAAC,CAAA;QAChE,CAAC;IACL,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,cAAc,CAAC,SAAiB;QAC5C,IAAI,SAAS,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACrC,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAA;YAClD,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAChC,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,SAAS,CACpD,CAAA;YAED,IAAI,KAAK,EAAE,CAAC;gBACR,OAAO,KAAK,CAAA;YAChB,CAAC;QACL,CAAC;QAED,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,CAAC,CAAA;QAEtD,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACzB,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAA;YAExD,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CACtC,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,cAAc,CACzD,CAAA;YAED,IAAI,CAAC,WAAW,EAAE,CAAC;gBACf,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,YAAY,CAChD,WAAW,CAAC,CAAC,CAAC,CACjB,CAAA;gBACD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAA;gBACtC,OAAO,WAAW,CAAC,CAAC,CAAC,CAAA;YACzB,CAAC;iBAAM,CAAC;gBACJ,OAAO,WAAW,CAAA;YACtB,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,MAAM,IAAI,2BAAY,CAAC,UAAU,SAAS,mBAAmB,CAAC,CAAA;QAClE,CAAC;IACL,CAAC;IAED;;OAEG;IACO,kBAAkB,CAAC,KAAY,EAAE,YAAmB;QAC1D,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAA;QAC7C,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CACrC,CAAC,WAAW,EAAE,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,YAAY,CACnE,CAAA;QAED,8BAA8B;QAC9B,KAAK,MAAM,CAAC,GAAG,EAAE,UAAU,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC;YACpE,IAAI,UAAU,KAAK,YAAY,EAAE,CAAC;gBAC9B,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,CAAA;YAChE,CAAC;QACL,CAAC;QAED,IAAI,UAAU,EAAE,CAAC;YACb,UAAU,CAAC,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAA;YAC3C,UAAU,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,CAAA;YACvC,UAAU,CAAC,IAAI,GAAG,YAAY,CAAC,IAAI,CAAA;YACnC,UAAU,CAAC,OAAO,GAAG,YAAY,CAAC,OAAO,CAAA;YACzC,UAAU,CAAC,OAAO,GAAG,YAAY,CAAC,OAAO,CAAA;YACzC,UAAU,CAAC,WAAW,GAAG,YAAY,CAAC,WAAW,CAAA;YACjD,UAAU,CAAC,OAAO,GAAG,YAAY,CAAC,OAAO,CAAA;YACzC,UAAU,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,CAAA;YACvC,UAAU,CAAC,WAAW,GAAG,YAAY,CAAC,WAAW,CAAA;YACjD,UAAU,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,CAAA;YACvC,UAAU,CAAC,OAAO,GAAG,YAAY,CAAC,OAAO,CAAA;QAC7C,CAAC;IACL,CAAC;IAES,YAAY,CAClB,MAAgE;QAEhE,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;QAE5D,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CACxC,MAAM,CAAC,SAAS,EAChB,MAAM,CAAC,MAAM,EACb,MAAM,CAAC,QAAQ,CAClB,CAAA;IACL,CAAC;IAES,2BAA2B;QACjC,MAAM,OAAO,GAEZ,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAA;QAC/B,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CACxC,IAAI,CAAC,UAAU,CAAC,iBAAiB,EACjC,OAAO,CAAC,MAAM,EACd,OAAO,CAAC,QAAQ,CACnB,CAAA;IACL,CAAC;IAED;;OAEG;IACO,wBAAwB,CAAC,EAC/B,QAAQ,EACR,MAAM,EACN,KAAK,EACL,IAAI,EACJ,IAAI,GAOP;QACG,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,kBAAkB,EAAE,CAAA;QAC/C,MAAM,QAAQ,GAAG,EAAE;aACd,MAAM,EAAE;aACR,IAAI,CAAC,IAAI,CAAC,2BAA2B,EAAE,EAAE,GAAG,CAAC;aAC7C,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE,IAAI,EAAE,CAAC;aAC/C,QAAQ,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE,IAAI,EAAE,CAAC,CAAA;QAEvD,IAAI,QAAQ,EAAE,CAAC;YACX,QAAQ,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,cAAc,EAAE;gBACtD,QAAQ;aACX,CAAC,CAAA;QACN,CAAC;QAED,IAAI,MAAM,EAAE,CAAC;YACT,QAAQ,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,EAAE,EAAE,MAAM,EAAE,CAAC,CAAA;QACrE,CAAC;QAED,IAAI,KAAK,EAAE,CAAC;YACR,QAAQ,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,KAAK,EAAE,CAAC,CAAA;QAClE,CAAC;QAED,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,GAAG,QAAQ,CAAC,qBAAqB,EAAE,CAAA;QAC5D,OAAO,IAAI,aAAK,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;IACvC,CAAC;IAED;;OAEG;IACO,wBAAwB,CAAC,EAC/B,QAAQ,EACR,MAAM,EACN,KAAK,EACL,IAAI,EACJ,IAAI,EACJ,KAAK,GAQR;QACG,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,GAAG,IAAI,CAAC,UAAU;aACtC,kBAAkB,EAAE;aACpB,MAAM,EAAE;aACR,IAAI,CAAC,IAAI,CAAC,2BAA2B,EAAE,CAAC;aACxC,MAAM,CAAC;YACJ,QAAQ,EAAE,QAAQ;YAClB,MAAM,EAAE,MAAM;YACd,KAAK,EAAE,KAAK;YACZ,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,KAAK;SACf,CAAC;aACD,qBAAqB,EAAE,CAAA;QAE5B,OAAO,IAAI,aAAK,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;IACvC,CAAC;IAED;;OAEG;IACO,wBAAwB,CAAC,EAC/B,QAAQ,EACR,MAAM,EACN,KAAK,EACL,IAAI,EACJ,IAAI,GAOP;QACG,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,kBAAkB,EAAE,CAAA;QAC/C,MAAM,QAAQ,GAAG,EAAE;aACd,MAAM,EAAE;aACR,IAAI,CAAC,IAAI,CAAC,2BAA2B,EAAE,CAAC;aACxC,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE,IAAI,EAAE,CAAC;aAC/C,QAAQ,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE,IAAI,EAAE,CAAC,CAAA;QAEvD,IAAI,QAAQ,EAAE,CAAC;YACX,QAAQ,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,cAAc,EAAE;gBACtD,QAAQ;aACX,CAAC,CAAA;QACN,CAAC;QAED,IAAI,MAAM,EAAE,CAAC;YACT,QAAQ,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,EAAE,EAAE,MAAM,EAAE,CAAC,CAAA;QACrE,CAAC;QAED,IAAI,KAAK,EAAE,CAAC;YACR,QAAQ,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,KAAK,EAAE,CAAC,CAAA;QAClE,CAAC;QAED,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,GAAG,QAAQ,CAAC,qBAAqB,EAAE,CAAA;QAC5D,OAAO,IAAI,aAAK,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;IACvC,CAAC;IAED;;;OAGG;IACO,eAAe,CACrB,SAAsB,EACtB,SAAsB,EACtB,YAAsB,EACtB,YAAsB,EACtB,SAAS,GAAG,IAAI;QAEhB,+EAA+E;QAE/E,0CAA0C;QAC1C,wDAAwD;QACxD,qDAAqD;QACrD,4CAA4C;QAC5C,4DAA4D;QAC5D,yDAAyD;QACzD,4CAA4C;QAC5C,4DAA4D;QAC5D,yDAAyD;QACzD,wCAAwC;QACxC,oDAAoD;QACpD,iDAAiD;QACjD,0CAA0C;QAC1C,0EAA0E;QAC1E,qDAAqD;QACrD,6CAA6C;QAC7C,8DAA8D;QAC9D,2DAA2D;QAC3D,0CAA0C;QAC1C,0EAA0E;QAC1E,qDAAqD;QACrD,uCAAuC;QACvC,oFAAoF;QACpF,+CAA+C;QAE/C,OAAO,CACH,SAAS,CAAC,OAAO,KAAK,SAAS,CAAC,OAAO;YACvC,SAAS,CAAC,SAAS,KAAK,SAAS,CAAC,SAAS;YAC3C,SAAS,CAAC,SAAS,KAAK,SAAS,CAAC,SAAS;YAC3C,SAAS,CAAC,KAAK,KAAK,SAAS,CAAC,KAAK;YACnC,SAAS,CAAC,KAAK,KAAK,SAAS,CAAC,KAAK,IAAI,aAAa;YACpD,SAAS,CAAC,QAAQ,KAAK,SAAS,CAAC,QAAQ,IAAI,aAAa;YAC1D,SAAS,CAAC,QAAQ,KAAK,SAAS,CAAC,QAAQ,IAAI,aAAa;YAC1D,SAAS,CAAC,YAAY,KAAK,SAAS,CAAC,YAAY;YACjD,CAAC,YAAY,IAAI,SAAS,CAAC,OAAO,KAAK,SAAS,CAAC,OAAO,CAAC;YACzD,SAAS,CAAC,QAAQ,KAAK,SAAS,CAAC,QAAQ,IAAI,aAAa;YAC1D,SAAS,CAAC,UAAU,KAAK,SAAS,CAAC,UAAU;YAC7C,CAAC,YAAY,IAAI,SAAS,CAAC,OAAO,KAAK,SAAS,CAAC,OAAO,CAAC;YACzD,CAAC,SAAS,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,CAC1D,CAAA;IACL,CAAC;IAES,aAAa,CAAC,SAAsB,EAAE,SAAsB;QAClE,OAAO,CAAC,mBAAQ,CAAC,aAAa,CAC1B,SAAS,CAAC,IAAI,IAAI,EAAE,EACpB,SAAS,CAAC,IAAI,IAAI,EAAE,CACvB,CAAA;IACL,CAAC;IAED;;OAEG;IACO,qBAAqB,CAC3B,KAAY,EACZ,MAAmB,EACnB,MAAc;QAEd,6EAA6E;QAC7E,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;YAC1C,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;YACxD,MAAM,cAAc,GAAG,QAAQ,CAAC,0BAA0B,CACtD,MAAM,CAAC,IAAI,CACd,CAAA;YAED,IAAI,cAAc,EAAE,CAAC;gBACjB,MAAM,oBAAoB,GACtB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC,CAAA;gBAC1D,IAAI,oBAAoB;oBAAE,OAAO,KAAK,CAAA;YAC1C,CAAC;QACL,CAAC;QAED,IACI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,gBAAgB;YACvC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC;YACpD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,EAC7D,CAAC;YACC,OAAO,CACH,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,gBAAgB,CACnC,MAAM,CAAC,IAAI,CACd,CAAC,MAAO,CAAC,QAAQ,EAAE,KAAK,MAAM,CAAC,QAAQ,EAAE,CAC7C,CAAA;QACL,CAAC;QAED,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACO,wBAAwB,CAC9B,KAAY,EACZ,MAAmB,EACnB,SAAiB;QAEjB,6EAA6E;QAC7E,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;YAC1C,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;YACxD,MAAM,cAAc,GAAG,QAAQ,CAAC,0BAA0B,CACtD,MAAM,CAAC,IAAI,CACd,CAAA;YACD,IACI,cAAc;gBACd,cAAc,CAAC,SAAS,KAAK,IAAI;gBACjC,cAAc,CAAC,SAAS,KAAK,SAAS;gBAEtC,OAAO,KAAK,CAAA;QACpB,CAAC;QAED,IACI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,gBAAgB;YACvC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC;YACpD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS;gBAC1D,IAAI;YACR,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS;gBAC1D,SAAS;YAEb,OAAO,CACH,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC;iBAC/C,SAAS,KAAK,SAAS,CAC/B,CAAA;QAEL,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACO,oBAAoB,CAC1B,KAAY,EACZ,MAAmB,EACnB,KAAa;QAEb,6EAA6E;QAC7E,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;YAC1C,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;YACxD,MAAM,cAAc,GAAG,QAAQ,CAAC,0BAA0B,CACtD,MAAM,CAAC,IAAI,CACd,CAAA;YACD,IACI,cAAc;gBACd,cAAc,CAAC,KAAK,KAAK,IAAI;gBAC7B,cAAc,CAAC,KAAK,KAAK,SAAS;gBAElC,OAAO,KAAK,CAAA;QACpB,CAAC;QAED,IACI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,gBAAgB;YACvC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC;YACpD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK;gBACtD,IAAI;YACR,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK;gBACtD,SAAS;YAEb,OAAO,CACH,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK;gBAC1D,KAAK,CACR,CAAA;QAEL,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,cAAc,CAC1B,SAA0B,EAC1B,WAA4B;QAE5B,IAAI,iCAAe,CAAC,OAAO,CAAC,SAAS,CAAC;YAAE,SAAS,GAAG,CAAC,SAAS,CAAC,CAAA;QAC/D,IAAI,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAAE,WAAW,GAAG,CAAC,WAAW,CAAC,CAAA;QAErE,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,CAAA;QAC7C,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,CAAA;QAEjD,8EAA8E;QAC9E,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI;YAC3B,OAAO,OAAO,CAAC,OAAO,EAAkB,CAAA;QAE5C,KAAK,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,IAAI,SAAS,EAAE,CAAC;YAC5C,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;QACvC,CAAC;IACL,CAAC;IAED;;OAEG;IACO,iBAAiB,CACvB,KAAmB,EACnB,KAAiB;QAEjB,sFAAsF;QACtF,OAAO,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CAC3C,KAAK,EACL,KAAK,CAAC,WAAW,EACjB,KAAK,CAAC,KAAK,CACd,CAAA;IACL,CAAC;CACJ;AAtqBD,0CAsqBC", "file": "BaseQueryRunner.js", "sourcesContent": ["import { PostgresConnectionOptions } from \"../driver/postgres/PostgresConnectionOptions\"\nimport { Query } from \"../driver/Query\"\nimport { SqlInMemory } from \"../driver/SqlInMemory\"\nimport { SqlServerConnectionOptions } from \"../driver/sqlserver/SqlServerConnectionOptions\"\nimport { TableIndex } from \"../schema-builder/table/TableIndex\"\nimport { View } from \"../schema-builder/view/View\"\nimport { DataSource } from \"../data-source/DataSource\"\nimport { Table } from \"../schema-builder/table/Table\"\nimport { EntityManager } from \"../entity-manager/EntityManager\"\nimport { TableColumn } from \"../schema-builder/table/TableColumn\"\nimport { Broadcaster } from \"../subscriber/Broadcaster\"\nimport { ReplicationMode } from \"../driver/types/ReplicationMode\"\nimport { TypeORMError } from \"../error/TypeORMError\"\nimport { EntityMetadata } from \"../metadata/EntityMetadata\"\nimport { TableForeignKey } from \"../schema-builder/table/TableForeignKey\"\nimport { OrmUtils } from \"../util/OrmUtils\"\nimport { MetadataTableType } from \"../driver/types/MetadataTableType\"\nimport { InstanceChecker } from \"../util/InstanceChecker\"\nimport { buildSqlTag } from \"../util/SqlTagUtils\"\n\nexport abstract class BaseQueryRunner {\n    // -------------------------------------------------------------------------\n    // Public Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Connection used by this query runner.\n     */\n    connection: DataSource\n\n    /**\n     * Entity manager working only with current query runner.\n     */\n    manager: EntityManager\n\n    /**\n     * Indicates if connection for this query runner is released.\n     * Once its released, query runner cannot run queries anymore.\n     */\n    isReleased = false\n\n    /**\n     * Indicates if transaction is in progress.\n     */\n    isTransactionActive = false\n\n    /**\n     * Stores temporarily user data.\n     * Useful for sharing data with subscribers.\n     */\n    data = {}\n\n    /**\n     * All synchronized tables in the database.\n     */\n    loadedTables: Table[] = []\n\n    /**\n     * All synchronized views in the database.\n     */\n    loadedViews: View[] = []\n\n    /**\n     * Broadcaster used on this query runner to broadcast entity events.\n     */\n    broadcaster: Broadcaster\n\n    // -------------------------------------------------------------------------\n    // Protected Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Real database connection from a connection pool used to perform queries.\n     */\n    protected databaseConnection: any\n\n    /**\n     * Indicates if special query runner mode in which sql queries won't be executed is enabled.\n     */\n    protected sqlMemoryMode: boolean = false\n\n    /**\n     * Sql-s stored if \"sql in memory\" mode is enabled.\n     */\n    protected sqlInMemory: SqlInMemory = new SqlInMemory()\n\n    /**\n     * Mode in which query runner executes.\n     * Used for replication.\n     * If replication is not setup its value is ignored.\n     */\n    protected mode: ReplicationMode\n\n    /**\n     * current depth of transaction.\n     * for transactionDepth > 0 will use SAVEPOINT to start and commit/rollback transaction blocks\n     */\n    protected transactionDepth = 0\n\n    private cachedTablePaths: Record<string, string> = {}\n\n    // -------------------------------------------------------------------------\n    // Public Abstract Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Executes a given SQL query.\n     */\n    abstract query(\n        query: string,\n        parameters?: any[],\n        useStructuredResult?: boolean,\n    ): Promise<any>\n\n    /**\n     * Tagged template function that executes raw SQL query and returns raw database results.\n     * Template expressions are automatically transformed into database parameters.\n     * Raw query execution is supported only by relational databases (MongoDB is not supported).\n     * Note: Don't call this as a regular function, it is meant to be used with backticks to tag a template literal.\n     * Example: queryRunner.sql`SELECT * FROM table_name WHERE id = ${id}`\n     */\n    async sql<T = any>(\n        strings: TemplateStringsArray,\n        ...values: unknown[]\n    ): Promise<T> {\n        const { query, parameters } = buildSqlTag({\n            driver: this.connection.driver,\n            strings: strings,\n            expressions: values,\n        })\n\n        return await this.query(query, parameters)\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Abstract Methods\n    // -------------------------------------------------------------------------\n\n    protected abstract loadTables(tablePaths?: string[]): Promise<Table[]>\n\n    protected abstract loadViews(tablePaths?: string[]): Promise<View[]>\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Called before migrations are run.\n     */\n    async beforeMigration(): Promise<void> {\n        // Do nothing\n    }\n\n    /**\n     * Called after migrations are run.\n     */\n    async afterMigration(): Promise<void> {\n        // Do nothing\n    }\n\n    /**\n     * Loads given table's data from the database.\n     */\n    async getTable(tablePath: string): Promise<Table | undefined> {\n        this.loadedTables = await this.loadTables([tablePath])\n        return this.loadedTables.length > 0 ? this.loadedTables[0] : undefined\n    }\n\n    /**\n     * Loads all tables (with given names) from the database.\n     */\n    async getTables(tableNames?: string[]): Promise<Table[]> {\n        if (!tableNames) {\n            // Don't cache in this case.\n            // This is the new case & isn't used anywhere else anyway.\n            return await this.loadTables(tableNames)\n        }\n\n        this.loadedTables = await this.loadTables(tableNames)\n        return this.loadedTables\n    }\n\n    /**\n     * Loads given view's data from the database.\n     */\n    async getView(viewPath: string): Promise<View | undefined> {\n        this.loadedViews = await this.loadViews([viewPath])\n        return this.loadedViews.length > 0 ? this.loadedViews[0] : undefined\n    }\n\n    /**\n     * Loads given view's data from the database.\n     */\n    async getViews(viewPaths?: string[]): Promise<View[]> {\n        this.loadedViews = await this.loadViews(viewPaths)\n        return this.loadedViews\n    }\n\n    /**\n     * Enables special query runner mode in which sql queries won't be executed,\n     * instead they will be memorized into a special variable inside query runner.\n     * You can get memorized sql using getMemorySql() method.\n     */\n    enableSqlMemory(): void {\n        this.sqlInMemory = new SqlInMemory()\n        this.sqlMemoryMode = true\n    }\n\n    /**\n     * Disables special query runner mode in which sql queries won't be executed\n     * started by calling enableSqlMemory() method.\n     *\n     * Previously memorized sql will be flushed.\n     */\n    disableSqlMemory(): void {\n        this.sqlInMemory = new SqlInMemory()\n        this.sqlMemoryMode = false\n    }\n\n    /**\n     * Flushes all memorized sqls.\n     */\n    clearSqlMemory(): void {\n        this.sqlInMemory = new SqlInMemory()\n    }\n\n    /**\n     * Gets sql stored in the memory. Parameters in the sql are already replaced.\n     */\n    getMemorySql(): SqlInMemory {\n        return this.sqlInMemory\n    }\n\n    /**\n     * Executes up sql queries.\n     */\n    async executeMemoryUpSql(): Promise<void> {\n        for (const { query, parameters } of this.sqlInMemory.upQueries) {\n            await this.query(query, parameters)\n        }\n    }\n\n    /**\n     * Executes down sql queries.\n     */\n    async executeMemoryDownSql(): Promise<void> {\n        for (const {\n            query,\n            parameters,\n        } of this.sqlInMemory.downQueries.reverse()) {\n            await this.query(query, parameters)\n        }\n    }\n\n    getReplicationMode(): ReplicationMode {\n        return this.mode\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Gets view from previously loaded views, otherwise loads it from database.\n     */\n    protected async getCachedView(viewName: string): Promise<View> {\n        const view = this.loadedViews.find((view) => view.name === viewName)\n        if (view) return view\n\n        const foundViews = await this.loadViews([viewName])\n        if (foundViews.length > 0) {\n            this.loadedViews.push(foundViews[0])\n            return foundViews[0]\n        } else {\n            throw new TypeORMError(`View \"${viewName}\" does not exist.`)\n        }\n    }\n\n    /**\n     * Gets table from previously loaded tables, otherwise loads it from database.\n     */\n    protected async getCachedTable(tableName: string): Promise<Table> {\n        if (tableName in this.cachedTablePaths) {\n            const tablePath = this.cachedTablePaths[tableName]\n            const table = this.loadedTables.find(\n                (table) => this.getTablePath(table) === tablePath,\n            )\n\n            if (table) {\n                return table\n            }\n        }\n\n        const foundTables = await this.loadTables([tableName])\n\n        if (foundTables.length > 0) {\n            const foundTablePath = this.getTablePath(foundTables[0])\n\n            const cachedTable = this.loadedTables.find(\n                (table) => this.getTablePath(table) === foundTablePath,\n            )\n\n            if (!cachedTable) {\n                this.cachedTablePaths[tableName] = this.getTablePath(\n                    foundTables[0],\n                )\n                this.loadedTables.push(foundTables[0])\n                return foundTables[0]\n            } else {\n                return cachedTable\n            }\n        } else {\n            throw new TypeORMError(`Table \"${tableName}\" does not exist.`)\n        }\n    }\n\n    /**\n     * Replaces loaded table with given changed table.\n     */\n    protected replaceCachedTable(table: Table, changedTable: Table): void {\n        const oldTablePath = this.getTablePath(table)\n        const foundTable = this.loadedTables.find(\n            (loadedTable) => this.getTablePath(loadedTable) === oldTablePath,\n        )\n\n        // Clean up the lookup cache..\n        for (const [key, cachedPath] of Object.entries(this.cachedTablePaths)) {\n            if (cachedPath === oldTablePath) {\n                this.cachedTablePaths[key] = this.getTablePath(changedTable)\n            }\n        }\n\n        if (foundTable) {\n            foundTable.database = changedTable.database\n            foundTable.schema = changedTable.schema\n            foundTable.name = changedTable.name\n            foundTable.columns = changedTable.columns\n            foundTable.indices = changedTable.indices\n            foundTable.foreignKeys = changedTable.foreignKeys\n            foundTable.uniques = changedTable.uniques\n            foundTable.checks = changedTable.checks\n            foundTable.justCreated = changedTable.justCreated\n            foundTable.engine = changedTable.engine\n            foundTable.comment = changedTable.comment\n        }\n    }\n\n    protected getTablePath(\n        target: EntityMetadata | Table | View | TableForeignKey | string,\n    ): string {\n        const parsed = this.connection.driver.parseTableName(target)\n\n        return this.connection.driver.buildTableName(\n            parsed.tableName,\n            parsed.schema,\n            parsed.database,\n        )\n    }\n\n    protected getTypeormMetadataTableName(): string {\n        const options = <\n            SqlServerConnectionOptions | PostgresConnectionOptions\n        >this.connection.driver.options\n        return this.connection.driver.buildTableName(\n            this.connection.metadataTableName,\n            options.schema,\n            options.database,\n        )\n    }\n\n    /**\n     * Generates SQL query to select record from typeorm metadata table.\n     */\n    protected selectTypeormMetadataSql({\n        database,\n        schema,\n        table,\n        type,\n        name,\n    }: {\n        database?: string\n        schema?: string\n        table?: string\n        type: MetadataTableType\n        name: string\n    }): Query {\n        const qb = this.connection.createQueryBuilder()\n        const selectQb = qb\n            .select()\n            .from(this.getTypeormMetadataTableName(), \"t\")\n            .where(`${qb.escape(\"type\")} = :type`, { type })\n            .andWhere(`${qb.escape(\"name\")} = :name`, { name })\n\n        if (database) {\n            selectQb.andWhere(`${qb.escape(\"database\")} = :database`, {\n                database,\n            })\n        }\n\n        if (schema) {\n            selectQb.andWhere(`${qb.escape(\"schema\")} = :schema`, { schema })\n        }\n\n        if (table) {\n            selectQb.andWhere(`${qb.escape(\"table\")} = :table`, { table })\n        }\n\n        const [query, parameters] = selectQb.getQueryAndParameters()\n        return new Query(query, parameters)\n    }\n\n    /**\n     * Generates SQL query to insert a record into typeorm metadata table.\n     */\n    protected insertTypeormMetadataSql({\n        database,\n        schema,\n        table,\n        type,\n        name,\n        value,\n    }: {\n        database?: string\n        schema?: string\n        table?: string\n        type: MetadataTableType\n        name: string\n        value?: string\n    }): Query {\n        const [query, parameters] = this.connection\n            .createQueryBuilder()\n            .insert()\n            .into(this.getTypeormMetadataTableName())\n            .values({\n                database: database,\n                schema: schema,\n                table: table,\n                type: type,\n                name: name,\n                value: value,\n            })\n            .getQueryAndParameters()\n\n        return new Query(query, parameters)\n    }\n\n    /**\n     * Generates SQL query to delete a record from typeorm metadata table.\n     */\n    protected deleteTypeormMetadataSql({\n        database,\n        schema,\n        table,\n        type,\n        name,\n    }: {\n        database?: string\n        schema?: string\n        table?: string\n        type: MetadataTableType\n        name: string\n    }): Query {\n        const qb = this.connection.createQueryBuilder()\n        const deleteQb = qb\n            .delete()\n            .from(this.getTypeormMetadataTableName())\n            .where(`${qb.escape(\"type\")} = :type`, { type })\n            .andWhere(`${qb.escape(\"name\")} = :name`, { name })\n\n        if (database) {\n            deleteQb.andWhere(`${qb.escape(\"database\")} = :database`, {\n                database,\n            })\n        }\n\n        if (schema) {\n            deleteQb.andWhere(`${qb.escape(\"schema\")} = :schema`, { schema })\n        }\n\n        if (table) {\n            deleteQb.andWhere(`${qb.escape(\"table\")} = :table`, { table })\n        }\n\n        const [query, parameters] = deleteQb.getQueryAndParameters()\n        return new Query(query, parameters)\n    }\n\n    /**\n     * Checks if at least one of column properties was changed.\n     * Does not checks column type, length and autoincrement, because these properties changes separately.\n     */\n    protected isColumnChanged(\n        oldColumn: TableColumn,\n        newColumn: TableColumn,\n        checkDefault?: boolean,\n        checkComment?: boolean,\n        checkEnum = true,\n    ): boolean {\n        // this logs need to debug issues in column change detection. Do not delete it!\n\n        // console.log(\"charset ---------------\");\n        // console.log(oldColumn.charset !== newColumn.charset);\n        // console.log(oldColumn.charset, newColumn.charset);\n        // console.log(\"collation ---------------\");\n        // console.log(oldColumn.collation !== newColumn.collation);\n        // console.log(oldColumn.collation, newColumn.collation);\n        // console.log(\"precision ---------------\");\n        // console.log(oldColumn.precision !== newColumn.precision);\n        // console.log(oldColumn.precision, newColumn.precision);\n        // console.log(\"scale ---------------\");\n        // console.log(oldColumn.scale !== newColumn.scale);\n        // console.log(oldColumn.scale, newColumn.scale);\n        // console.log(\"default ---------------\");\n        // console.log((checkDefault && oldColumn.default !== newColumn.default));\n        // console.log(oldColumn.default, newColumn.default);\n        // console.log(\"isNullable ---------------\");\n        // console.log(oldColumn.isNullable !== newColumn.isNullable);\n        // console.log(oldColumn.isNullable, newColumn.isNullable);\n        // console.log(\"comment ---------------\");\n        // console.log((checkComment && oldColumn.comment !== newColumn.comment));\n        // console.log(oldColumn.comment, newColumn.comment);\n        // console.log(\"enum ---------------\");\n        // console.log(!OrmUtils.isArraysEqual(oldColumn.enum || [], newColumn.enum || []));\n        // console.log(oldColumn.enum, newColumn.enum);\n\n        return (\n            oldColumn.charset !== newColumn.charset ||\n            oldColumn.collation !== newColumn.collation ||\n            oldColumn.precision !== newColumn.precision ||\n            oldColumn.scale !== newColumn.scale ||\n            oldColumn.width !== newColumn.width || // MySQL only\n            oldColumn.zerofill !== newColumn.zerofill || // MySQL only\n            oldColumn.unsigned !== newColumn.unsigned || // MySQL only\n            oldColumn.asExpression !== newColumn.asExpression ||\n            (checkDefault && oldColumn.default !== newColumn.default) ||\n            oldColumn.onUpdate !== newColumn.onUpdate || // MySQL only\n            oldColumn.isNullable !== newColumn.isNullable ||\n            (checkComment && oldColumn.comment !== newColumn.comment) ||\n            (checkEnum && this.isEnumChanged(oldColumn, newColumn))\n        )\n    }\n\n    protected isEnumChanged(oldColumn: TableColumn, newColumn: TableColumn) {\n        return !OrmUtils.isArraysEqual(\n            oldColumn.enum || [],\n            newColumn.enum || [],\n        )\n    }\n\n    /**\n     * Checks if column length is by default.\n     */\n    protected isDefaultColumnLength(\n        table: Table,\n        column: TableColumn,\n        length: string,\n    ): boolean {\n        // if table have metadata, we check if length is specified in column metadata\n        if (this.connection.hasMetadata(table.name)) {\n            const metadata = this.connection.getMetadata(table.name)\n            const columnMetadata = metadata.findColumnWithDatabaseName(\n                column.name,\n            )\n\n            if (columnMetadata) {\n                const columnMetadataLength =\n                    this.connection.driver.getColumnLength(columnMetadata)\n                if (columnMetadataLength) return false\n            }\n        }\n\n        if (\n            this.connection.driver.dataTypeDefaults &&\n            this.connection.driver.dataTypeDefaults[column.type] &&\n            this.connection.driver.dataTypeDefaults[column.type].length\n        ) {\n            return (\n                this.connection.driver.dataTypeDefaults[\n                    column.type\n                ].length!.toString() === length.toString()\n            )\n        }\n\n        return false\n    }\n\n    /**\n     * Checks if column precision is by default.\n     */\n    protected isDefaultColumnPrecision(\n        table: Table,\n        column: TableColumn,\n        precision: number,\n    ): boolean {\n        // if table have metadata, we check if length is specified in column metadata\n        if (this.connection.hasMetadata(table.name)) {\n            const metadata = this.connection.getMetadata(table.name)\n            const columnMetadata = metadata.findColumnWithDatabaseName(\n                column.name,\n            )\n            if (\n                columnMetadata &&\n                columnMetadata.precision !== null &&\n                columnMetadata.precision !== undefined\n            )\n                return false\n        }\n\n        if (\n            this.connection.driver.dataTypeDefaults &&\n            this.connection.driver.dataTypeDefaults[column.type] &&\n            this.connection.driver.dataTypeDefaults[column.type].precision !==\n                null &&\n            this.connection.driver.dataTypeDefaults[column.type].precision !==\n                undefined\n        )\n            return (\n                this.connection.driver.dataTypeDefaults[column.type]\n                    .precision === precision\n            )\n\n        return false\n    }\n\n    /**\n     * Checks if column scale is by default.\n     */\n    protected isDefaultColumnScale(\n        table: Table,\n        column: TableColumn,\n        scale: number,\n    ): boolean {\n        // if table have metadata, we check if length is specified in column metadata\n        if (this.connection.hasMetadata(table.name)) {\n            const metadata = this.connection.getMetadata(table.name)\n            const columnMetadata = metadata.findColumnWithDatabaseName(\n                column.name,\n            )\n            if (\n                columnMetadata &&\n                columnMetadata.scale !== null &&\n                columnMetadata.scale !== undefined\n            )\n                return false\n        }\n\n        if (\n            this.connection.driver.dataTypeDefaults &&\n            this.connection.driver.dataTypeDefaults[column.type] &&\n            this.connection.driver.dataTypeDefaults[column.type].scale !==\n                null &&\n            this.connection.driver.dataTypeDefaults[column.type].scale !==\n                undefined\n        )\n            return (\n                this.connection.driver.dataTypeDefaults[column.type].scale ===\n                scale\n            )\n\n        return false\n    }\n\n    /**\n     * Executes sql used special for schema build.\n     */\n    protected async executeQueries(\n        upQueries: Query | Query[],\n        downQueries: Query | Query[],\n    ): Promise<void> {\n        if (InstanceChecker.isQuery(upQueries)) upQueries = [upQueries]\n        if (InstanceChecker.isQuery(downQueries)) downQueries = [downQueries]\n\n        this.sqlInMemory.upQueries.push(...upQueries)\n        this.sqlInMemory.downQueries.push(...downQueries)\n\n        // if sql-in-memory mode is enabled then simply store sql in memory and return\n        if (this.sqlMemoryMode === true)\n            return Promise.resolve() as Promise<any>\n\n        for (const { query, parameters } of upQueries) {\n            await this.query(query, parameters)\n        }\n    }\n\n    /**\n     * Generated an index name for a table and index\n     */\n    protected generateIndexName(\n        table: Table | View,\n        index: TableIndex,\n    ): string {\n        // new index may be passed without name. In this case we generate index name manually.\n        return this.connection.namingStrategy.indexName(\n            table,\n            index.columnNames,\n            index.where,\n        )\n    }\n}\n"], "sourceRoot": ".."}