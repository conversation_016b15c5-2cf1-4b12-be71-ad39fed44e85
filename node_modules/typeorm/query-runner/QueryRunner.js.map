{"version": 3, "sources": ["../../src/query-runner/QueryRunner.ts"], "names": [], "mappings": "", "file": "QueryRunner.js", "sourcesContent": ["import { TableColumn } from \"../schema-builder/table/TableColumn\"\nimport { Table } from \"../schema-builder/table/Table\"\nimport { TableForeignKey } from \"../schema-builder/table/TableForeignKey\"\nimport { TableIndex } from \"../schema-builder/table/TableIndex\"\nimport { DataSource } from \"../data-source/DataSource\"\nimport { ReadStream } from \"../platform/PlatformTools\"\nimport { EntityManager } from \"../entity-manager/EntityManager\"\nimport { ObjectLiteral } from \"../common/ObjectLiteral\"\nimport { SqlInMemory } from \"../driver/SqlInMemory\"\nimport { TableUnique } from \"../schema-builder/table/TableUnique\"\nimport { View } from \"../schema-builder/view/View\"\nimport { Broadcaster } from \"../subscriber/Broadcaster\"\nimport { TableCheck } from \"../schema-builder/table/TableCheck\"\nimport { IsolationLevel } from \"../driver/types/IsolationLevel\"\nimport { TableExclusion } from \"../schema-builder/table/TableExclusion\"\nimport { QueryResult } from \"./QueryResult\"\nimport { ReplicationMode } from \"../driver/types/ReplicationMode\"\n\n/**\n * Runs queries on a single database connection.\n */\nexport interface QueryRunner {\n    /**\n     * Connection used by this query runner.\n     */\n    readonly connection: DataSource\n\n    /**\n     * Broadcaster used on this query runner to broadcast entity events.\n     */\n    readonly broadcaster: Broadcaster\n\n    /**\n     * Entity manager working only with this query runner.\n     */\n    readonly manager: EntityManager\n\n    /**\n     * Indicates if connection for this query runner is released.\n     * Once its released, query runner cannot run queries anymore.\n     */\n    readonly isReleased: boolean\n\n    /**\n     * Indicates if transaction is in progress.\n     */\n    readonly isTransactionActive: boolean\n\n    /**\n     * Stores temporarily user data.\n     * Useful for sharing data with subscribers.\n     */\n    data: ObjectLiteral\n\n    /**\n     * All synchronized tables in the database.\n     *\n     * @deprecated Call `getTables()`\n     */\n    loadedTables: Table[]\n\n    /**\n     * All synchronized views in the database.\n     *\n     * @deprecated Call `getViews()`\n     */\n    loadedViews: View[]\n\n    /**\n     * Creates/uses database connection from the connection pool to perform further operations.\n     * Returns obtained database connection.\n     */\n    connect(): Promise<any>\n\n    /**\n     * Called before migrations are run.\n     */\n    beforeMigration(): Promise<void>\n\n    /**\n     * Called after migrations are run.\n     */\n    afterMigration(): Promise<void>\n\n    /**\n     * Releases used database connection.\n     * You cannot use query runner methods after connection is released.\n     */\n    release(): Promise<void>\n\n    /**\n     * Removes all tables from the currently connected database.\n     * Be careful with using this method and avoid using it in production or migrations\n     * (because it can clear all your database).\n     */\n    clearDatabase(database?: string): Promise<void>\n\n    /**\n     * Starts transaction.\n     */\n    startTransaction(isolationLevel?: IsolationLevel): Promise<void>\n\n    /**\n     * Commits transaction.\n     * Error will be thrown if transaction was not started.\n     */\n    commitTransaction(): Promise<void>\n\n    /**\n     * Rollbacks transaction.\n     * Error will be thrown if transaction was not started.\n     */\n    rollbackTransaction(): Promise<void>\n\n    /**\n     * Executes a given SQL query and returns raw database results.\n     */\n    query(\n        query: string,\n        parameters: any[] | undefined,\n        useStructuredResult: true,\n    ): Promise<QueryResult>\n\n    /**\n     * Executes a given SQL query and returns raw database results.\n     */\n    query(query: string, parameters?: any[]): Promise<any>\n\n    /**\n     * Tagged template function that executes raw SQL query and returns raw database results.\n     * Template expressions are automatically transformed into database parameters.\n     * Raw query execution is supported only by relational databases (MongoDB is not supported).\n     * Note: Don't call this as a regular function, it is meant to be used with backticks to tag a template literal.\n     * Example: queryRunner.sql`SELECT * FROM table_name WHERE id = ${id}`\n     */\n    sql<T = any>(\n        strings: TemplateStringsArray,\n        ...values: unknown[]\n    ): Promise<T>\n\n    /**\n     * Returns raw data stream.\n     */\n    stream(\n        query: string,\n        parameters?: any[],\n        onEnd?: Function,\n        onError?: Function,\n    ): Promise<ReadStream>\n\n    /**\n     * Returns all available database names including system databases.\n     */\n    getDatabases(): Promise<string[]>\n\n    /**\n     * Returns all available schema names including system schemas.\n     * If database parameter specified, returns schemas of that database.\n     * Useful for SQLServer and Postgres only.\n     */\n    getSchemas(database?: string): Promise<string[]>\n\n    /**\n     * Loads a table by a given name from the database.\n     */\n    getTable(tablePath: string): Promise<Table | undefined>\n\n    /**\n     * Loads all tables from the database and returns them.\n     */\n    getTables(tablePaths?: string[]): Promise<Table[]>\n\n    /**\n     * Loads a view by a given name from the database.\n     */\n    getView(viewPath: string): Promise<View | undefined>\n\n    /**\n     * Loads all views from the database and returns them.\n     */\n    getViews(viewPaths?: string[]): Promise<View[]>\n\n    /**\n     * Returns replication mode (ex: `master` or `slave`).\n     */\n    getReplicationMode(): ReplicationMode\n\n    /**\n     * Checks if a database with the given name exist.\n     */\n    hasDatabase(database: string): Promise<boolean>\n\n    /**\n     * Loads currently using database\n     */\n    getCurrentDatabase(): Promise<string | undefined>\n\n    /**\n     * Checks if a schema with the given name exist.\n     */\n    hasSchema(schema: string): Promise<boolean>\n\n    /**\n     * Loads currently using database schema\n     */\n    getCurrentSchema(): Promise<string | undefined>\n\n    /**\n     * Checks if a table with the given name exist.\n     */\n    hasTable(table: Table | string): Promise<boolean>\n\n    /**\n     * Checks if a column exist in the table.\n     */\n    hasColumn(table: Table | string, columnName: string): Promise<boolean>\n\n    /**\n     * Creates a new database.\n     */\n    createDatabase(database: string, ifNotExist?: boolean): Promise<void>\n\n    /**\n     * Drops database.\n     */\n    dropDatabase(database: string, ifExist?: boolean): Promise<void>\n\n    /**\n     * Creates a new table schema.\n     */\n    createSchema(schemaPath: string, ifNotExist?: boolean): Promise<void>\n\n    /**\n     * Drops table schema.\n     * For SqlServer can accept schema path (e.g. 'dbName.schemaName') as parameter.\n     * If schema path passed, it will drop schema in specified database.\n     */\n    dropSchema(\n        schemaPath: string,\n        ifExist?: boolean,\n        isCascade?: boolean,\n    ): Promise<void>\n\n    /**\n     * Creates a new table.\n     */\n    createTable(\n        table: Table,\n        ifNotExist?: boolean,\n        createForeignKeys?: boolean,\n        createIndices?: boolean,\n    ): Promise<void>\n\n    /**\n     * Drops a table.\n     */\n    dropTable(\n        table: Table | string,\n        ifExist?: boolean,\n        dropForeignKeys?: boolean,\n        dropIndices?: boolean,\n    ): Promise<void>\n\n    /**\n     * Creates a new view.\n     */\n    createView(\n        view: View,\n        syncWithMetadata?: boolean,\n        oldView?: View,\n    ): Promise<void>\n\n    /**\n     * Drops a view.\n     */\n    dropView(view: View | string): Promise<void>\n\n    /**\n     * Renames a table.\n     */\n    renameTable(\n        oldTableOrName: Table | string,\n        newTableName: string,\n    ): Promise<void>\n\n    /**\n     * Change table comment. Only supports MySQL and MariaDB\n     */\n    changeTableComment(\n        tableOrName: Table | string,\n        comment?: string,\n    ): Promise<void>\n\n    /**\n     * Adds a new column.\n     */\n    addColumn(table: Table | string, column: TableColumn): Promise<void>\n\n    /**\n     * Adds new columns.\n     */\n    addColumns(table: Table | string, columns: TableColumn[]): Promise<void>\n\n    /**\n     * Renames a column.\n     */\n    renameColumn(\n        table: Table | string,\n        oldColumnOrName: TableColumn | string,\n        newColumnOrName: TableColumn | string,\n    ): Promise<void>\n\n    /**\n     * Changes a column in the table.\n     */\n    changeColumn(\n        table: Table | string,\n        oldColumn: TableColumn | string,\n        newColumn: TableColumn,\n    ): Promise<void>\n\n    /**\n     * Changes columns in the table.\n     */\n    changeColumns(\n        table: Table | string,\n        changedColumns: { oldColumn: TableColumn; newColumn: TableColumn }[],\n    ): Promise<void>\n\n    /**\n     * Drops a column in the table.\n     */\n    dropColumn(\n        table: Table | string,\n        column: TableColumn | string,\n    ): Promise<void>\n\n    /**\n     * Drops columns in the table.\n     */\n    dropColumns(\n        table: Table | string,\n        columns: TableColumn[] | string[],\n    ): Promise<void>\n\n    /**\n     * Creates a new primary key.\n     */\n    createPrimaryKey(\n        table: Table | string,\n        columnNames: string[],\n        constraintName?: string,\n    ): Promise<void>\n\n    /**\n     * Updates composite primary keys.\n     */\n    updatePrimaryKeys(\n        table: Table | string,\n        columns: TableColumn[],\n    ): Promise<void>\n\n    /**\n     * Drops a primary key.\n     */\n    dropPrimaryKey(\n        table: Table | string,\n        constraintName?: string,\n    ): Promise<void>\n\n    /**\n     * Creates a new unique constraint.\n     */\n    createUniqueConstraint(\n        table: Table | string,\n        uniqueConstraint: TableUnique,\n    ): Promise<void>\n\n    /**\n     * Creates new unique constraints.\n     */\n    createUniqueConstraints(\n        table: Table | string,\n        uniqueConstraints: TableUnique[],\n    ): Promise<void>\n\n    /**\n     * Drops an unique constraint.\n     */\n    dropUniqueConstraint(\n        table: Table | string,\n        uniqueOrName: TableUnique | string,\n    ): Promise<void>\n\n    /**\n     * Drops unique constraints.\n     */\n    dropUniqueConstraints(\n        table: Table | string,\n        uniqueConstraints: TableUnique[],\n    ): Promise<void>\n\n    /**\n     * Creates a new check constraint.\n     */\n    createCheckConstraint(\n        table: Table | string,\n        checkConstraint: TableCheck,\n    ): Promise<void>\n\n    /**\n     * Creates new check constraints.\n     */\n    createCheckConstraints(\n        table: Table | string,\n        checkConstraints: TableCheck[],\n    ): Promise<void>\n\n    /**\n     * Drops a check constraint.\n     */\n    dropCheckConstraint(\n        table: Table | string,\n        checkOrName: TableCheck | string,\n    ): Promise<void>\n\n    /**\n     * Drops check constraints.\n     */\n    dropCheckConstraints(\n        table: Table | string,\n        checkConstraints: TableCheck[],\n    ): Promise<void>\n\n    /**\n     * Creates a new exclusion constraint.\n     */\n    createExclusionConstraint(\n        table: Table | string,\n        exclusionConstraint: TableExclusion,\n    ): Promise<void>\n\n    /**\n     * Creates new exclusion constraints.\n     */\n    createExclusionConstraints(\n        table: Table | string,\n        exclusionConstraints: TableExclusion[],\n    ): Promise<void>\n\n    /**\n     * Drops a exclusion constraint.\n     */\n    dropExclusionConstraint(\n        table: Table | string,\n        exclusionOrName: TableExclusion | string,\n    ): Promise<void>\n\n    /**\n     * Drops exclusion constraints.\n     */\n    dropExclusionConstraints(\n        table: Table | string,\n        exclusionConstraints: TableExclusion[],\n    ): Promise<void>\n\n    /**\n     * Creates a new foreign key.\n     */\n    createForeignKey(\n        table: Table | string,\n        foreignKey: TableForeignKey,\n    ): Promise<void>\n\n    /**\n     * Creates new foreign keys.\n     */\n    createForeignKeys(\n        table: Table | string,\n        foreignKeys: TableForeignKey[],\n    ): Promise<void>\n\n    /**\n     * Drops a foreign key.\n     */\n    dropForeignKey(\n        table: Table | string,\n        foreignKeyOrName: TableForeignKey | string,\n    ): Promise<void>\n\n    /**\n     * Drops foreign keys.\n     */\n    dropForeignKeys(\n        table: Table | string,\n        foreignKeys: TableForeignKey[],\n    ): Promise<void>\n\n    /**\n     * Creates a new index.\n     */\n    createIndex(table: Table | string, index: TableIndex): Promise<void>\n\n    /**\n     * Creates new indices.\n     */\n    createIndices(table: Table | string, indices: TableIndex[]): Promise<void>\n\n    /**\n     * Drops an index.\n     */\n    dropIndex(table: Table | string, index: TableIndex | string): Promise<void>\n\n    /**\n     * Drops indices.\n     */\n    dropIndices(table: Table | string, indices: TableIndex[]): Promise<void>\n\n    /**\n     * Clears all table contents.\n     * Note: this operation uses SQL's TRUNCATE query which cannot be reverted in transactions.\n     */\n    clearTable(tableName: string): Promise<void>\n\n    /**\n     * Enables special query runner mode in which sql queries won't be executed,\n     * instead they will be memorized into a special variable inside query runner.\n     * You can get memorized sql using getMemorySql() method.\n     */\n    enableSqlMemory(): void\n\n    /**\n     * Disables special query runner mode in which sql queries won't be executed\n     * started by calling enableSqlMemory() method.\n     *\n     * Previously memorized sql will be flushed.\n     */\n    disableSqlMemory(): void\n\n    /**\n     * Flushes all memorized sqls.\n     */\n    clearSqlMemory(): void\n\n    /**\n     * Gets sql stored in the memory. Parameters in the sql are already replaced.\n     */\n    getMemorySql(): SqlInMemory\n\n    /**\n     * Executes up sql queries.\n     */\n    executeMemoryUpSql(): Promise<void>\n\n    /**\n     * Executes down sql queries.\n     */\n    executeMemoryDownSql(): Promise<void>\n}\n"], "sourceRoot": ".."}