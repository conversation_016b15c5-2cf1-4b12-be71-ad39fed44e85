{"version": 3, "sources": ["../../src/query-builder/relation-count/RelationCountMetadataToAttributeTransformer.ts"], "names": [], "mappings": ";;;AAEA,qEAAiE;AAEjE,MAAa,2CAA2C;IACpD,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAsB,aAAiC;QAAjC,kBAAa,GAAb,aAAa,CAAoB;IAAG,CAAC;IAE3D,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E,SAAS;QACL,cAAc;QACd,2BAA2B;QAC3B,wDAAwD;QACxD,8BAA8B;QAC9B,yDAAyD;QACzD,iCAAiC;QACjC,oFAAoF;QAEpF,0HAA0H;QAC1H,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC;YAC/B,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,QAAQ,CAAC,cAAc,CAAC,OAAO,CACxD,CAAC,aAAa,EAAE,EAAE;gBACd,MAAM,SAAS,GAAG,IAAI,CAAC,mBAAmB,CACtC,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,IAAI,EAClC,aAAa,CAChB,CAAA;gBACD,IAAI,CAAC,aAAa,CAAC,uBAAuB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;YAC9D,CAAC,CACJ,CAAA;QACL,CAAC;QAED,yHAAyH;QACzH,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YAC/C,8FAA8F;YAC9F,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU;gBAAE,OAAM;YAEtD,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,EAAE;gBACnD,MAAM,SAAS,GAAG,IAAI,CAAC,mBAAmB,CACtC,IAAI,CAAC,KAAK,CAAC,IAAI,EACf,aAAa,CAChB,CAAA;gBACD,IAAI,CAAC,aAAa,CAAC,uBAAuB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;YAC9D,CAAC,CAAC,CAAA;QACN,CAAC,CAAC,CAAA;IACN,CAAC;IAED,4EAA4E;IAC5E,kBAAkB;IAClB,4EAA4E;IAEpE,mBAAmB,CACvB,eAAuB,EACvB,aAAoC;QAEpC,OAAO,IAAI,+CAAsB,CAAC,IAAI,CAAC,aAAa,EAAE;YAClD,YAAY,EACR,eAAe,GAAG,GAAG,GAAG,aAAa,CAAC,QAAQ,CAAC,YAAY,EAAE,kBAAkB;YACnF,aAAa,EAAE,eAAe,GAAG,GAAG,GAAG,aAAa,CAAC,YAAY,EAAE,oBAAoB;YACvF,KAAK,EAAE,aAAa,CAAC,KAAK;YAC1B,mBAAmB,EAAE,aAAa,CAAC,mBAAmB;SACzD,CAAC,CAAA;IACN,CAAC;CACJ;AAhED,kGAgEC", "file": "RelationCountMetadataToAttributeTransformer.js", "sourcesContent": ["import { QueryExpressionMap } from \"../QueryExpressionMap\"\nimport { RelationCountMetadata } from \"../../metadata/RelationCountMetadata\"\nimport { RelationCountAttribute } from \"./RelationCountAttribute\"\n\nexport class RelationCountMetadataToAttributeTransformer {\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(protected expressionMap: QueryExpressionMap) {}\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    transform() {\n        // by example:\n        // post has relation count:\n        // @RelationCount(post => post.categories) categoryCount\n        // category has relation count\n        // @RelationCount(category => category.images) imageCount\n        // we load post and join category\n        // we expect post.categoryCount and post.category.imageCount to have relation counts\n\n        // first create relation count attributes for all relation count metadatas of the main selected object (post from example)\n        if (this.expressionMap.mainAlias) {\n            this.expressionMap.mainAlias.metadata.relationCounts.forEach(\n                (relationCount) => {\n                    const attribute = this.metadataToAttribute(\n                        this.expressionMap.mainAlias!.name,\n                        relationCount,\n                    )\n                    this.expressionMap.relationCountAttributes.push(attribute)\n                },\n            )\n        }\n\n        // second create relation count attributes for all relation count metadatas of all joined objects (category from example)\n        this.expressionMap.joinAttributes.forEach((join) => {\n            // ensure this join has a metadata, because relation count can only work for real orm entities\n            if (!join.metadata || join.metadata.isJunction) return\n\n            join.metadata.relationCounts.forEach((relationCount) => {\n                const attribute = this.metadataToAttribute(\n                    join.alias.name,\n                    relationCount,\n                )\n                this.expressionMap.relationCountAttributes.push(attribute)\n            })\n        })\n    }\n\n    // -------------------------------------------------------------------------\n    // Private Methods\n    // -------------------------------------------------------------------------\n\n    private metadataToAttribute(\n        parentAliasName: string,\n        relationCount: RelationCountMetadata,\n    ): RelationCountAttribute {\n        return new RelationCountAttribute(this.expressionMap, {\n            relationName:\n                parentAliasName + \".\" + relationCount.relation.propertyName, // category.images\n            mapToProperty: parentAliasName + \".\" + relationCount.propertyName, // category.imageIds\n            alias: relationCount.alias,\n            queryBuilderFactory: relationCount.queryBuilderFactory,\n        })\n    }\n}\n"], "sourceRoot": "../.."}