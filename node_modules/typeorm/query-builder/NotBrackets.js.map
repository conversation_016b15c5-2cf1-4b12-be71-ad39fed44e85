{"version": 3, "sources": ["../../src/query-builder/NotBrackets.ts"], "names": [], "mappings": ";;;AAAA,yCAAqC;AAErC;;;GAGG;AACH,MAAa,WAAY,SAAQ,mBAAQ;IAAzC;;QACa,mBAAa,GAAG,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC,CAAA;IACtD,CAAC;CAAA;AAFD,kCAEC", "file": "NotBrackets.js", "sourcesContent": ["import { Brackets } from \"./Brackets\"\n\n/**\n * Syntax sugar.\n * Allows to use negate brackets in WHERE expressions for better syntax.\n */\nexport class NotBrackets extends Brackets {\n    readonly \"@instanceof\" = Symbol.for(\"NotBrackets\")\n}\n"], "sourceRoot": ".."}