{"version": 3, "sources": ["../../src/query-builder/result/UpdateResult.ts"], "names": [], "mappings": ";;;AAGA;;GAEG;AACH,MAAa,YAAY;IAAzB;QAmBI;;;WAGG;QACH,oCAAoC;QAEpC;;;WAGG;QACH,kBAAa,GAAoB,EAAE,CAAA;IACvC,CAAC;IA7BG,MAAM,CAAC,IAAI,CAAC,WAAwB;QAChC,MAAM,MAAM,GAAG,IAAI,IAAI,EAAE,CAAA;QACzB,MAAM,CAAC,GAAG,GAAG,WAAW,CAAC,OAAO,CAAA;QAChC,MAAM,CAAC,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAA;QACtC,OAAO,MAAM,CAAA;IACjB,CAAC;CAwBJ;AA9BD,oCA8BC", "file": "UpdateResult.js", "sourcesContent": ["import { ObjectLiteral } from \"../../common/ObjectLiteral\"\nimport { QueryResult } from \"../../query-runner/QueryResult\"\n\n/**\n * Result object returned by UpdateQueryBuilder execution.\n */\nexport class UpdateResult {\n    static from(queryResult: QueryResult) {\n        const result = new this()\n        result.raw = queryResult.records\n        result.affected = queryResult.affected\n        return result\n    }\n\n    /**\n     * Raw SQL result returned by executed query.\n     */\n    raw: any\n\n    /**\n     * Number of affected rows/documents\n     * Not all drivers support this\n     */\n    affected?: number\n\n    /**\n     * Contains inserted entity id.\n     * Has entity-like structure (not just column database name and values).\n     */\n    // identifier: ObjectLiteral[] = [];\n\n    /**\n     * Generated values returned by a database.\n     * Has entity-like structure (not just column database name and values).\n     */\n    generatedMaps: ObjectLiteral[] = []\n}\n"], "sourceRoot": "../.."}