{"version": 3, "sources": ["../../src/query-builder/transformer/PlainObjectToDatabaseEntityTransformer.ts"], "names": [], "mappings": ";;;AAKA;GACG;AACH,MAAM,WAAW;IAOb,YACI,WAA0B,EAC1B,QAAwB,EACxB,iBAA+B,EAC/B,QAA2B;QAE3B,IAAI,CAAC,WAAW,GAAG,WAAW,CAAA;QAC9B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAA;QACxB,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAA;QAC1C,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAA;IAC5B,CAAC;IAED,IAAI,MAAM;QACN,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAA;IAC/B,CAAC;IAED,IAAI,EAAE;QACF,OAAO,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;IAC9D,CAAC;CACJ;AAED,MAAM,OAAO;IAAb;QACI,iBAAY,GAAkB,EAAE,CAAA;IA8CpC,CAAC;IA5CG,IAAI,eAAe;QACf,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CACzB,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,iBAAiB,CACtD,CAAA;IACL,CAAC;IAED,UAAU,CAAC,UAAuB;QAC9B,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAC/B,CAAC,IAAI,EAAE,EAAE,CACL,IAAI,CAAC,MAAM,KAAK,UAAU,CAAC,MAAM,IAAI,IAAI,CAAC,EAAE,KAAK,UAAU,CAAC,EAAE,CACrE,CAAA;QACD,IAAI,CAAC,IAAI;YAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;IACjD,CAAC;IAED,YAAY,CAAC,MAAyB,EAAE,QAAe;QACnD,QAAQ,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YACxB,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,EAAE;gBAChD,OAAO,CACH,WAAW,CAAC,MAAM,KAAK,MAAM;oBAC7B,WAAW,CAAC,QAAQ,CAAC,eAAe,CAChC,MAAM,EACN,WAAW,CAAC,WAAW,CAC1B,CACJ,CAAA;YACL,CAAC,CAAC,CAAA;YACF,IAAI,IAAI;gBAAE,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QAClC,CAAC,CAAC,CAAA;IACN,CAAC;IAED,gBAAgB;QACZ,MAAM,MAAM,GAAgD,EAAE,CAAA;QAC9D,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,EAAE;YACtC,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,CACnB,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK,WAAW,CAAC,MAAM,CACjD,CAAA;YACD,IAAI,CAAC,KAAK,EAAE,CAAC;gBACT,KAAK,GAAG,EAAE,MAAM,EAAE,WAAW,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,CAAA;gBAC/C,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YACtB,CAAC;YAED,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAA;QAClC,CAAC,CAAC,CAAA;QACF,OAAO,MAAM,CAAA;IACjB,CAAC;CACJ;AAED;;;GAGG;AACH,MAAa,sCAAsC;IAC/C,YAAoB,OAAsB;QAAtB,YAAO,GAAP,OAAO,CAAe;IAAG,CAAC;IAE9C,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E,KAAK,CAAC,SAAS,CACX,WAA0B,EAC1B,QAAwB;QAExB,+DAA+D;QAC/D,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,WAAW,CAAC;YACxC,OAAO,OAAO,CAAC,MAAM,CACjB,sFAAsF,CACzF,CAAA;QAEL,0GAA0G;QAC1G,MAAM,OAAO,GAAG,IAAI,OAAO,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAG,CAChB,MAAqB,EACrB,cAA8B,EAC9B,iBAA+B,EAC/B,QAA2B,EAC7B,EAAE;YACA,MAAM,IAAI,GAAG,IAAI,WAAW,CACxB,MAAM,EACN,cAAc,EACd,iBAAiB,EACjB,QAAQ,CACX,CAAA;YACD,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;YAExB,cAAc;iBACT,+BAA+B,CAAC,MAAM,EAAE,QAAQ,CAAC,SAAS,CAAC;iBAC3D,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,CAAC;iBACxD,OAAO,CAAC,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,qBAAqB,CAAC,EAAE,EAAE,CAClD,WAAW,CAAC,KAAK,EAAE,qBAAqB,EAAE,IAAI,EAAE,QAAQ,CAAC,CAC5D,CAAA;QACT,CAAC,CAAA;QACD,WAAW,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAA;QAClC,mDAAmD;QACnD,MAAM,OAAO,CAAC,GAAG,CACb,OAAO,CAAC,gBAAgB,EAAE,CAAC,GAAG,CAAC,CAAC,aAAa,EAAE,EAAE;YAC7C,yBAAyB;YACzB,OAAO,IAAI,CAAC,OAAO;iBACd,SAAS,CACN,aAAa,CAAC,MAAa,EAC3B,aAAa,CAAC,GAAG,CACpB;iBACA,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE,CACf,OAAO,CAAC,YAAY,CAAC,aAAa,CAAC,MAAM,EAAE,QAAQ,CAAC,CACvD,CAAA;QACT,CAAC,CAAC,CACL,CAAA;QAED,2GAA2G;QAC3G,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,EAAE;YACzC,IACI,CAAC,WAAW,CAAC,QAAQ;gBACrB,CAAC,WAAW,CAAC,MAAM;gBACnB,CAAC,WAAW,CAAC,iBAAiB;gBAC9B,CAAC,WAAW,CAAC,iBAAiB,CAAC,MAAM;gBAErC,OAAM;YAEV,IACI,WAAW,CAAC,QAAQ,CAAC,YAAY;gBACjC,WAAW,CAAC,QAAQ,CAAC,WAAW,EAClC,CAAC;gBACC,IACI,CAAC,WAAW,CAAC,iBAAiB,CAAC,MAAM,CACjC,WAAW,CAAC,QAAQ,CAAC,YAAY,CACpC;oBAED,WAAW,CAAC,iBAAiB,CAAC,MAAM,CAChC,WAAW,CAAC,QAAQ,CAAC,YAAY,CACpC,GAAG,EAAE,CAAA;gBACV,WAAW,CAAC,iBAAiB,CAAC,MAAM,CAChC,WAAW,CAAC,QAAQ,CAAC,YAAY,CACpC,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;YAC9B,CAAC;iBAAM,CAAC;gBACJ,WAAW,CAAC,iBAAiB,CAAC,MAAM,CAChC,WAAW,CAAC,QAAQ,CAAC,YAAY,CACpC,GAAG,WAAW,CAAC,MAAM,CAAA;YAC1B,CAAC;QACL,CAAC,CAAC,CAAA;QAEF,OAAO,OAAO,CAAC,eAAe;YAC1B,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC,MAAM;YAChC,CAAC,CAAC,SAAS,CAAA;IACnB,CAAC;CACJ;AA5FD,wFA4FC", "file": "PlainObjectToDatabaseEntityTransformer.js", "sourcesContent": ["import { ObjectLiteral } from \"../../common/ObjectLiteral\"\nimport { EntityMetadata } from \"../../metadata/EntityMetadata\"\nimport { EntityManager } from \"../../entity-manager/EntityManager\"\nimport { RelationMetadata } from \"../../metadata/RelationMetadata\"\n\n/**\n */\nclass LoadMapItem {\n    entity?: ObjectLiteral\n    plainEntity: ObjectLiteral\n    metadata: EntityMetadata\n    parentLoadMapItem?: LoadMapItem\n    relation?: RelationMetadata\n\n    constructor(\n        plainEntity: ObjectLiteral,\n        metadata: EntityMetadata,\n        parentLoadMapItem?: LoadMapItem,\n        relation?: RelationMetadata,\n    ) {\n        this.plainEntity = plainEntity\n        this.metadata = metadata\n        this.parentLoadMapItem = parentLoadMapItem\n        this.relation = relation\n    }\n\n    get target(): Function | string {\n        return this.metadata.target\n    }\n\n    get id(): any {\n        return this.metadata.getEntityIdMixedMap(this.plainEntity)\n    }\n}\n\nclass LoadMap {\n    loadMapItems: LoadMapItem[] = []\n\n    get mainLoadMapItem(): LoadMapItem | undefined {\n        return this.loadMapItems.find(\n            (item) => !item.relation && !item.parentLoadMapItem,\n        )\n    }\n\n    addLoadMap(newLoadMap: LoadMapItem) {\n        const item = this.loadMapItems.find(\n            (item) =>\n                item.target === newLoadMap.target && item.id === newLoadMap.id,\n        )\n        if (!item) this.loadMapItems.push(newLoadMap)\n    }\n\n    fillEntities(target: Function | string, entities: any[]) {\n        entities.forEach((entity) => {\n            const item = this.loadMapItems.find((loadMapItem) => {\n                return (\n                    loadMapItem.target === target &&\n                    loadMapItem.metadata.compareEntities(\n                        entity,\n                        loadMapItem.plainEntity,\n                    )\n                )\n            })\n            if (item) item.entity = entity\n        })\n    }\n\n    groupByTargetIds(): { target: Function | string; ids: any[] }[] {\n        const groups: { target: Function | string; ids: any[] }[] = []\n        this.loadMapItems.forEach((loadMapItem) => {\n            let group = groups.find(\n                (group) => group.target === loadMapItem.target,\n            )\n            if (!group) {\n                group = { target: loadMapItem.target, ids: [] }\n                groups.push(group)\n            }\n\n            group.ids.push(loadMapItem.id)\n        })\n        return groups\n    }\n}\n\n/**\n * Transforms plain old javascript object\n * Entity is constructed based on its entity metadata.\n */\nexport class PlainObjectToDatabaseEntityTransformer {\n    constructor(private manager: EntityManager) {}\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    async transform(\n        plainObject: ObjectLiteral,\n        metadata: EntityMetadata,\n    ): Promise<ObjectLiteral | undefined> {\n        // if plain object does not have id then nothing to load really\n        if (!metadata.hasAllPrimaryKeys(plainObject))\n            return Promise.reject(\n                \"Given object does not have a primary column, cannot transform it to database entity.\",\n            )\n\n        // create a special load map that will hold all metadata that will be used to operate with entities easily\n        const loadMap = new LoadMap()\n        const fillLoadMap = (\n            entity: ObjectLiteral,\n            entityMetadata: EntityMetadata,\n            parentLoadMapItem?: LoadMapItem,\n            relation?: RelationMetadata,\n        ) => {\n            const item = new LoadMapItem(\n                entity,\n                entityMetadata,\n                parentLoadMapItem,\n                relation,\n            )\n            loadMap.addLoadMap(item)\n\n            entityMetadata\n                .extractRelationValuesFromEntity(entity, metadata.relations)\n                .filter((value) => value !== null && value !== undefined)\n                .forEach(([relation, value, inverseEntityMetadata]) =>\n                    fillLoadMap(value, inverseEntityMetadata, item, relation),\n                )\n        }\n        fillLoadMap(plainObject, metadata)\n        // load all entities and store them in the load map\n        await Promise.all(\n            loadMap.groupByTargetIds().map((targetWithIds) => {\n                // todo: fix type hinting\n                return this.manager\n                    .findByIds<ObjectLiteral>(\n                        targetWithIds.target as any,\n                        targetWithIds.ids,\n                    )\n                    .then((entities) =>\n                        loadMap.fillEntities(targetWithIds.target, entities),\n                    )\n            }),\n        )\n\n        // go through each item in the load map and set their entity relationship using metadata stored in load map\n        loadMap.loadMapItems.forEach((loadMapItem) => {\n            if (\n                !loadMapItem.relation ||\n                !loadMapItem.entity ||\n                !loadMapItem.parentLoadMapItem ||\n                !loadMapItem.parentLoadMapItem.entity\n            )\n                return\n\n            if (\n                loadMapItem.relation.isManyToMany ||\n                loadMapItem.relation.isOneToMany\n            ) {\n                if (\n                    !loadMapItem.parentLoadMapItem.entity[\n                        loadMapItem.relation.propertyName\n                    ]\n                )\n                    loadMapItem.parentLoadMapItem.entity[\n                        loadMapItem.relation.propertyName\n                    ] = []\n                loadMapItem.parentLoadMapItem.entity[\n                    loadMapItem.relation.propertyName\n                ].push(loadMapItem.entity)\n            } else {\n                loadMapItem.parentLoadMapItem.entity[\n                    loadMapItem.relation.propertyName\n                ] = loadMapItem.entity\n            }\n        })\n\n        return loadMap.mainLoadMapItem\n            ? loadMap.mainLoadMapItem.entity\n            : undefined\n    }\n}\n"], "sourceRoot": "../.."}