{"version": 3, "sources": ["../../src/query-builder/QueryBuilder.ts"], "names": [], "mappings": ";;;AAIA,6DAAyD;AASzD,yCAAqC;AAIrC,+DAA2D;AAC3D,oDAAgD;AAChD,oCAAuC;AAGvC,sFAAkF;AAGlF,6DAAyD;AACzD,uDAAmD;AAEnD,kDAAkD;AAClD,kFAAkF;AAClF,sGAAsG;AACtG,gEAAgE;AAChE,oEAAoE;AACpE,yDAAyD;AACzD,iCAAiC;AACjC,2GAA2G;AAC3G,oCAAoC;AACpC,qCAAqC;AAErC,mGAAmG;AACnG,+DAA+D;AAC/D,sDAAsD;AAEtD;;GAEG;AACH,MAAsB,YAAY;IAuD9B;;OAEG;IACH,YACI,wBAAwD,EACxD,WAAyB;QA3DpB,mBAAa,GAAG,MAAM,CAAC,GAAG,CAAC,cAAc,CAAC,CAAA;QA8BnD;;WAEG;QACK,mBAAc,GAAG,CAAC,CAAA;QA4BtB,IAAI,iCAAe,CAAC,YAAY,CAAC,wBAAwB,CAAC,EAAE,CAAC;YACzD,IAAI,CAAC,UAAU,GAAG,wBAAwB,CAAA;YAC1C,IAAI,CAAC,WAAW,GAAG,WAAW,CAAA;YAC9B,IAAI,CAAC,aAAa,GAAG,IAAI,uCAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;QAChE,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,UAAU,GAAG,wBAAwB,CAAC,UAAU,CAAA;YACrD,IAAI,CAAC,WAAW,GAAG,wBAAwB,CAAC,WAAW,CAAA;YACvD,IAAI,CAAC,aAAa,GAAG,wBAAwB,CAAC,aAAa,CAAC,KAAK,EAAE,CAAA;QACvE,CAAC;IACL,CAAC;IAED,MAAM,CAAC,yBAAyB,CAAC,IAAY,EAAE,OAAY;QACvD,YAAY,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,OAAO,CAAA;IACrD,CAAC;IAWD,4EAA4E;IAC5E,YAAY;IACZ,4EAA4E;IAE5E;;OAEG;IACH,IAAI,KAAK;QACL,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS;YAC7B,MAAM,IAAI,oBAAY,CAAC,uBAAuB,CAAC,CAAA,CAAC,yBAAyB;QAE7E,OAAO,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,IAAI,CAAA;IAC5C,CAAC;IA2BD;;;OAGG;IACH,MAAM,CACF,SAA6B,EAC7B,kBAA2B;QAE3B,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,QAAQ,CAAA;QACvC,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;YAC3B,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;gBACvD,SAAS,EAAE,SAAS;aACvB,CAAC,CAAC,CAAA;QACP,CAAC;aAAM,IAAI,SAAS,EAAE,CAAC;YACnB,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG;gBACzB,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,kBAAkB,EAAE;aAC1D,CAAA;QACL,CAAC;QAED,IAAI,iCAAe,CAAC,oBAAoB,CAAC,IAAI,CAAC;YAAE,OAAO,IAAW,CAAA;QAElE,OAAO,YAAY,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,CAAA;IACxE,CAAC;IAED;;OAEG;IACH,MAAM;QACF,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,QAAQ,CAAA;QAEvC,IAAI,iCAAe,CAAC,oBAAoB,CAAC,IAAI,CAAC;YAAE,OAAO,IAAW,CAAA;QAElE,OAAO,YAAY,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,CAAA;IACxE,CAAC;IA8BD;;OAEG;IACH,MAAM,CACF,0BAA8D,EAC9D,cAA8B;QAE9B,MAAM,SAAS,GAAG,cAAc;YAC5B,CAAC,CAAC,cAAc;YAChB,CAAC,CAAE,0BAAwD,CAAA;QAC/D,0BAA0B,GAAG,iCAAe,CAAC,cAAc,CACvD,0BAA0B,CAC7B;YACG,CAAC,CAAC,0BAA0B,CAAC,OAAO,CAAC,IAAI;YACzC,CAAC,CAAC,0BAA0B,CAAA;QAEhC,IACI,OAAO,0BAA0B,KAAK,UAAU;YAChD,OAAO,0BAA0B,KAAK,QAAQ,EAChD,CAAC;YACC,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,0BAA0B,CAAC,CAAA;YAClE,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,SAAS,CAAC,CAAA;QAC9C,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,QAAQ,CAAA;QACvC,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,SAAS,CAAA;QAExC,IAAI,iCAAe,CAAC,oBAAoB,CAAC,IAAI,CAAC;YAAE,OAAO,IAAW,CAAA;QAElE,OAAO,YAAY,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,CAAA;IACxE,CAAC;IAED;;OAEG;IACH,MAAM;QACF,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,QAAQ,CAAA;QAEvC,IAAI,iCAAe,CAAC,oBAAoB,CAAC,IAAI,CAAC;YAAE,OAAO,IAAW,CAAA;QAElE,OAAO,YAAY,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,CAAA;IACxE,CAAC;IAED,UAAU;QACN,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,aAAa,CAAA;QAE5C,IAAI,iCAAe,CAAC,wBAAwB,CAAC,IAAI,CAAC;YAAE,OAAO,IAAW,CAAA;QAEtE,OAAO,YAAY,CAAC,oBAAoB,CAAC,wBAAwB,CAAC,CAAC,IAAI,CAAC,CAAA;IAC5E,CAAC;IAED,OAAO;QACH,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,SAAS,CAAA;QAExC,IAAI,iCAAe,CAAC,wBAAwB,CAAC,IAAI,CAAC;YAAE,OAAO,IAAW,CAAA;QAEtE,OAAO,YAAY,CAAC,oBAAoB,CAAC,wBAAwB,CAAC,CAAC,IAAI,CAAC,CAAA;IAC5E,CAAC;IAeD;;OAEG;IACH,QAAQ,CACJ,0BAA6C,EAC7C,iBAA0B;QAE1B,MAAM,YAAY,GACd,SAAS,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,0BAA0B,CAAC,CAAC,CAAC,SAAS,CAAA;QACnE,MAAM,YAAY,GACd,SAAS,CAAC,MAAM,KAAK,CAAC;YAClB,CAAC,CAAE,iBAA4B;YAC/B,CAAC,CAAE,0BAAqC,CAAA;QAEhD,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,UAAU,CAAA;QACzC,IAAI,CAAC,aAAa,CAAC,oBAAoB,GAAG,YAAY,CAAA;QAEtD,IAAI,YAAY,EAAE,CAAC;YACf,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAA;YACpD,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,SAAS,CAAC,CAAA;QAC9C,CAAC;QAED,IAAI,iCAAe,CAAC,sBAAsB,CAAC,IAAI,CAAC;YAAE,OAAO,IAAW,CAAA;QAEpE,OAAO,YAAY,CAAC,oBAAoB,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,CAAA;IAC1E,CAAC;IAkBD;;;;;OAKG;IACH,WAAW,CACP,MAAuB,EACvB,QAA2B;QAE3B,MAAM,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;QAC1D,MAAM,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAA;QACjE,OAAO,SAAS,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,EAAE;YAChC,OAAO,CAAC,CAAC,cAAc,CAAC,4BAA4B,CAAC,QAAQ,CAAC,CAAA;QAClE,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,GAAW;QACpB,OAAO,CACH,IAAI,CAAC,kBAAkB,EAAE,YAAY,CAAC,GAAG,CAAC;YAC1C,GAAG,IAAI,IAAI,CAAC,aAAa,CAAC,UAAU,CACvC,CAAA;IACL,CAAC;IAED;;;;OAIG;IACH,YAAY,CAAC,GAAW,EAAE,KAAU;QAChC,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE,CAAC;YAC9B,MAAM,IAAI,oBAAY,CAClB,uEAAuE,GAAG,cAAc,CAC3F,CAAA;QACL,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,oBAAoB,CAAC,EAAE,CAAC;YACnC,MAAM,IAAI,oBAAY,CAClB,yFAAyF,CAC5F,CAAA;QACL,CAAC;QAED,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;QACpD,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,KAAK,CAAA;QAC1C,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,UAAyB;QACnC,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;YACpD,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;QACjC,CAAC;QAED,OAAO,IAAI,CAAA;IACf,CAAC;IAES,eAAe,CAAC,KAAU;QAChC,IAAI,aAAa,CAAA;QAEjB,GAAG,CAAC;YACA,aAAa,GAAG,aAAa,IAAI,CAAC,cAAc,EAAE,EAAE,CAAA;QACxD,CAAC,QAAQ,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,EAAC;QAE1C,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,KAAK,CAAC,CAAA;QAEvC,OAAO,IAAI,aAAa,EAAE,CAAA;IAC9B,CAAC;IAED;;;;OAIG;IACH,mBAAmB,CAAC,UAAyB;QACzC,gEAAgE;QAChE,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,IAAI,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAA;QAC3D,CAAC;QAED,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;YACpC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC,CAAA;QAC9D,CAAC,CAAC,CAAA;QACF,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;OAEG;IACH,aAAa;QACT,MAAM,UAAU,GAAkB,MAAM,CAAC,MAAM,CAC3C,EAAE,EACF,IAAI,CAAC,aAAa,CAAC,UAAU,CAChC,CAAA;QAED,iDAAiD;QACjD,IACI,IAAI,CAAC,aAAa,CAAC,SAAS;YAC5B,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,WAAW,EAC1C,CAAC;YACC,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ,CAAA;YACvD,IAAI,QAAQ,CAAC,mBAAmB,IAAI,QAAQ,CAAC,oBAAoB,EAAE,CAAC;gBAChE,MAAM,MAAM,GAAG,QAAQ,CAAC,oBAAoB;qBACvC,MAAM,CACH,CAAC,aAAa,EAAE,EAAE,CAAC,aAAa,CAAC,mBAAmB,CACvD;qBACA,GAAG,CAAC,CAAC,aAAa,EAAE,EAAE,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAA;gBAC7D,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAA;gBACxC,UAAU,CAAC,2BAA2B,CAAC,GAAG,MAAM,CAAA;YACpD,CAAC;QACL,CAAC;QAED,OAAO,UAAU,CAAA;IACrB,CAAC;IAED;;OAEG;IACH,QAAQ;QACJ,0BAA0B;QAC1B,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAA;QACxD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;QAClD,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;;OAGG;IACH,MAAM;QACF,OAAO,IAAI,CAAC,qBAAqB,EAAE,CAAC,CAAC,CAAC,CAAA;IAC1C,CAAC;IAED;;OAEG;IACH,qBAAqB;QACjB,iHAAiH;QACjH,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAA;QAC7B,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,CAAA;QACvC,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,yBAAyB,CACnD,KAAK,EACL,UAAU,EACV,IAAI,CAAC,aAAa,CAAC,gBAAgB,CACtC,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO;QACT,MAAM,CAAC,GAAG,EAAE,UAAU,CAAC,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAA;QACtD,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAA;QAC5C,IAAI,CAAC;YACD,OAAO,MAAM,WAAW,CAAC,KAAK,CAAC,GAAG,EAAE,UAAU,CAAC,CAAA,CAAC,oDAAoD;QACxG,CAAC;gBAAS,CAAC;YACP,IAAI,WAAW,KAAK,IAAI,CAAC,WAAW,EAAE,CAAC;gBACnC,wCAAwC;gBACxC,MAAM,WAAW,CAAC,OAAO,EAAE,CAAA;YAC/B,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,kBAAkB,CAAC,WAAyB;QACxC,OAAO,IAAK,IAAI,CAAC,WAAmB,CAChC,IAAI,CAAC,UAAU,EACf,WAAW,IAAI,IAAI,CAAC,WAAW,CAClC,CAAA;IACL,CAAC;IAED;;;;;OAKG;IACH,KAAK;QACD,OAAO,IAAK,IAAI,CAAC,WAAmB,CAAC,IAAI,CAAC,CAAA;IAC9C,CAAC;IAED;;;;OAIG;IACH,OAAO,CAAC,OAAe;QACnB,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,OAAO,CAAA;QACpC,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;OAEG;IACH,eAAe;QACX,IAAI,CAAC,aAAa,CAAC,eAAe,GAAG,KAAK,CAAA;QAC1C,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,IAAY;QACf,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe;YAAE,OAAO,IAAI,CAAA;QACpD,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;IAC9C,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,WAAwB;QACnC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAA;QAC9B,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;;OAGG;IACH,aAAa,CAAC,OAAgB;QAC1B,IAAI,CAAC,aAAa,CAAC,aAAa,GAAG,OAAO,CAAA;QAC1C,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,OAAgB;QAC3B,IAAI,CAAC,aAAa,CAAC,cAAc,GAAG,OAAO,CAAA;QAC3C,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;OAEG;IACH,wBAAwB,CACpB,YAAwC,EACxC,KAAa,EACb,OAAgC;QAEhC,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAAC,IAAI,CAAC;YAC3C,YAAY;YACZ,KAAK;YACL,OAAO,EAAE,OAAO,IAAI,EAAE;SACzB,CAAC,CAAA;QACF,OAAO,IAAI,CAAA;IACf,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;;OAGG;IACO,YAAY,CAAC,SAAiB;QACpC,OAAO,SAAS;aACX,KAAK,CAAC,GAAG,CAAC;aACV,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;YACP,sMAAsM;YACtM,IAAI,CAAC,KAAK,EAAE;gBAAE,OAAO,CAAC,CAAA;YACtB,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;QACzB,CAAC,CAAC;aACD,IAAI,CAAC,GAAG,CAAC,CAAA;IAClB,CAAC;IAED;;OAEG;IACO,gBAAgB;QACtB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS;YAC7B,MAAM,IAAI,oBAAY,CAClB,uGAAuG,CAC1G,CAAA;QAEL,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,WAAW;YACxC,OAAO,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAA;QAE1D,OAAO,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,SAAU,CAAA;IAClD,CAAC;IAED;;;OAGG;IACO,eAAe,CACrB,YAEgE,EAChE,SAAkB;QAElB,yEAAyE;QACzE,uGAAuG;QACvG,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,YAAY,CAAC,EAAE,CAAC;YAC5C,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,YAAY,CAAC,CAAA;YAE1D,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC;gBAClC,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,SAAS;gBACf,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,YAAY,CAAC;gBACnD,SAAS,EAAE,QAAQ,CAAC,SAAS;aAChC,CAAC,CAAA;QACN,CAAC;aAAM,CAAC;YACJ,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE,CAAC;gBACnC,MAAM,UAAU,GACZ,YAAY,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG;oBACjC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAA;gBAEnC,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC;oBAClC,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,SAAS;oBACf,SAAS,EAAE,CAAC,UAAU;wBAClB,CAAC,CAAE,YAAuB;wBAC1B,CAAC,CAAC,SAAS;oBACf,QAAQ,EAAE,UAAU,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS;iBAClD,CAAC,CAAA;YACN,CAAC;YAED,MAAM,eAAe,GACjB,YACH,CAAE,IAAuC,CAAC,QAAQ,EAAE,CAAC,CAAA;YACtD,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,aAAa,EAAE,CAAC,CAAA;YACnD,MAAM,QAAQ,GAAG,eAAe,CAAC,QAAQ,EAAE,CAAA;YAE3C,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC;gBAClC,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,SAAS;gBACf,QAAQ,EAAE,QAAQ;aACrB,CAAC,CAAA;QACN,CAAC;IACL,CAAC;IAED;;;OAGG;IACO,oBAAoB,CAAC,SAAiB;QAC5C,OAAO,SAAS,CAAA;IACpB,CAAC;IAED;;OAEG;IACO,oCAAoC,CAAC,SAAiB;QAC5D,MAAM,YAAY,GAAiD,EAAE,CAAA;QAErE,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;YAC7C,IAAI,CAAC,KAAK,CAAC,WAAW;gBAAE,SAAQ;YAChC,MAAM,sBAAsB,GACxB,IAAI,CAAC,aAAa,CAAC,yBAAyB,IAAI,KAAK,CAAC,IAAI;gBACtD,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,GAAG;gBAClB,CAAC,CAAC,EAAE,CAAA;YAEZ,IAAI,CAAC,YAAY,CAAC,sBAAsB,CAAC,EAAE,CAAC;gBACxC,YAAY,CAAC,sBAAsB,CAAC,GAAG,EAAE,CAAA;YAC7C,CAAC;YAED,8FAA8F;YAC9F,iEAAiE;YACjE,0BAA0B;YAC1B,oDAAoD;YACpD,yCAAyC;YACzC,yBAAyB;YACzB,yBAAyB;YACzB,yBAAyB;YAEzB,KAAK,MAAM,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC;gBAC9C,IAAI,QAAQ,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC;oBAC/B,YAAY,CAAC,sBAAsB,CAAC,CAChC,QAAQ,CAAC,YAAY,CACxB,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,YAAY,CAAA;YAChD,CAAC;YAED,KAAK,MAAM,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC;gBAC9C,MAAM,UAAU,GAAG;oBACf,GAAG,QAAQ,CAAC,WAAW;oBACvB,GAAG,QAAQ,CAAC,kBAAkB;iBACjC,CAAA;gBACD,KAAK,MAAM,UAAU,IAAI,UAAU,EAAE,CAAC;oBAClC,MAAM,WAAW,GAAG,GAAG,QAAQ,CAAC,YAAY,IACxC,UAAU,CAAC,gBAAiB,CAAC,YACjC,EAAE,CAAA;oBACF,YAAY,CAAC,sBAAsB,CAAC,CAAC,WAAW,CAAC;wBAC7C,UAAU,CAAC,YAAY,CAAA;gBAC/B,CAAC;YACL,CAAC;YAED,KAAK,MAAM,MAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;gBAC1C,YAAY,CAAC,sBAAsB,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC;oBACrD,MAAM,CAAC,YAAY,CAAA;YAC3B,CAAC;YAED,KAAK,MAAM,MAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;gBAC1C,YAAY,CAAC,sBAAsB,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC;oBACrD,MAAM,CAAC,YAAY,CAAA;YAC3B,CAAC;YAED,KAAK,MAAM,MAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;gBAC1C,YAAY,CAAC,sBAAsB,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC;oBACrD,MAAM,CAAC,YAAY,CAAA;YAC3B,CAAC;QACL,CAAC;QAED,MAAM,eAAe,GAAG,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;QACjD,MAAM,wBAAwB,GAAG,eAAe;aAC3C,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,IAAA,2BAAY,EAAC,GAAG,CAAC,CAAC;aAC/B,IAAI,CAAC,GAAG,CAAC,CAAA;QAEd,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7B,SAAS,GAAG,SAAS,CAAC,OAAO,CACzB,IAAI,MAAM;YACN,wDAAwD;YACxD,eAAe,GAAG,gCAAgC;gBAC9C,kDAAkD;gBAClD,GACI,wBAAwB;oBACpB,CAAC,CAAC,GAAG,GAAG,wBAAwB,GAAG,GAAG;oBACtC,CAAC,CAAC,EACV,aAAa,GAAG,6DAA6D;gBAC7E,sCAAsC;gBACtC,kBAAkB,EACtB,IAAI,CACP,EACD,CAAC,GAAG,OAAO,EAAE,EAAE;gBACX,IAAI,KAAa,EAAE,GAAW,EAAE,CAAS,CAAA;gBACzC,IAAI,wBAAwB,EAAE,CAAC;oBAC3B,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;oBAClB,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;oBAChB,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;oBAEd,IAAI,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;wBAC9B,OAAO,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,CACvB,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CACjD,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;oBACnD,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACJ,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;oBAClB,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;oBAChB,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;oBAEd,IAAI,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;wBACtB,OAAO,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;oBACtD,CAAC;gBACL,CAAC;gBACD,OAAO,KAAK,CAAA;YAChB,CAAC,CACJ,CAAA;QACL,CAAC;QAED,OAAO,SAAS,CAAA;IACpB,CAAC;IAES,aAAa;QACnB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;YAC9B,OAAO,EAAE,CAAA;QACb,CAAC;QAED,2FAA2F;QAC3F,0FAA0F;QAC1F,uFAAuF;QACvF,gCAAgC;QAEhC,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,MAAM,CAAA;IACtE,CAAC;IAED;;OAEG;IACO,qBAAqB;QAC3B,IACI,IAAI,CAAC,aAAa,CAAC,SAAS,KAAK,QAAQ;YACzC,IAAI,CAAC,aAAa,CAAC,UAAU,EAC/B,CAAC;YACC,OAAO,sBAAsB,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAA;QAChE,CAAC;QAED,OAAO,EAAE,CAAA;IACb,CAAC;IAED;;OAEG;IACO,qBAAqB;QAC3B,MAAM,eAAe,GAAG,EAAE,CAAA;QAE1B,MAAM,eAAe,GAAG,IAAI,CAAC,4BAA4B,CACrD,IAAI,CAAC,aAAa,CAAC,MAAM,CAC5B,CAAA;QAED,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,IAAI,eAAe,KAAK,KAAK,EAAE,CAAC;YAC1D,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,eAAe,CAAC,CAAC,CAAA;QACpE,CAAC;QAED,IAAI,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,WAAW,EAAE,CAAC;YAC5C,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ,CAAA;YACvD,sGAAsG;YACtG,IACI,IAAI,CAAC,aAAa,CAAC,SAAS,KAAK,QAAQ;gBACzC,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW;gBAC/B,QAAQ,CAAC,gBAAgB,EAC3B,CAAC;gBACC,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,yBAAyB;oBACvD,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,IAAI;wBAClC,GAAG;wBACH,QAAQ,CAAC,gBAAgB,CAAC,YAAY;oBACxC,CAAC,CAAC,QAAQ,CAAC,gBAAgB,CAAC,YAAY,CAAA;gBAE5C,MAAM,SAAS,GAAG,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,UAAU,CAAA;gBAChE,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;YACnC,CAAC;YAED,IAAI,QAAQ,CAAC,mBAAmB,IAAI,QAAQ,CAAC,oBAAoB,EAAE,CAAC;gBAChE,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,yBAAyB;oBACvD,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,IAAI;wBAClC,GAAG;wBACH,QAAQ,CAAC,mBAAmB,CAAC,YAAY;oBAC3C,CAAC,CAAC,QAAQ,CAAC,mBAAmB,CAAC,YAAY,CAAA;gBAE/C,MAAM,SAAS,GAAG,GAAG,IAAI,CAAC,oBAAoB,CAC1C,MAAM,CACT,qCAAqC,CAAA;gBACtC,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;YACnC,CAAC;QACL,CAAC;QAED,IAAI,IAAI,CAAC,aAAa,CAAC,8BAA8B,EAAE,CAAC;YACpD,MAAM,SAAS,GAAG,IAAI,CAAC,oBAAoB,CACvC,IAAI,CAAC,aAAa,CAAC,8BAA8B,CACpD,CAAA;YACD,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;QACnC,CAAC;QAED,IAAI,SAAS,GAAG,EAAE,CAAA;QAElB,cAAc;QACd,SAAS,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAA;QAEzC,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;YAC1B,SAAS,IAAI,EAAE,CAAA;QACnB,CAAC;aAAM,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACtC,SAAS,IAAI,UAAU,eAAe,CAAC,CAAC,CAAC,EAAE,CAAA;QAC/C,CAAC;aAAM,CAAC;YACJ,SAAS,IAAI,YAAY,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAA;QAClE,CAAC;QAED,OAAO,SAAS,CAAA;IACpB,CAAC;IAED;;OAEG;IACO,yBAAyB,CAAC,aAA4B;QAC5D,MAAM,OAAO,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAA;QAC1C,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAA;QAErC,kEAAkE;QAClE,iCAAiC;QACjC,IACI,OAAO,IAAI,CAAC,aAAa,CAAC,SAAS,KAAK,QAAQ;YAChD,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,MAAM,GAAG,CAAC;YACnD,MAAM,CAAC,uBAAuB,CAAC,aAAa,CAAC,EAC/C,CAAC;YACC,OAAO,CAAC,IAAI,CACR,GAAG,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE;gBAC1D,OAAO,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAA;YACzC,CAAC,CAAC,CACL,CAAA;QACL,CAAC;QAED,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACjB,IAAI,iBAAiB,GAAG,OAAO;iBAC1B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;gBACZ,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAA;gBAC7C,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;oBAClC,IACI,IAAI,CAAC,aAAa,CAAC,SAAS,KAAK,QAAQ;wBACzC,IAAI,CAAC,aAAa,CAAC,SAAS,KAAK,QAAQ;wBACzC,IAAI,CAAC,aAAa,CAAC,SAAS,KAAK,aAAa;wBAC9C,IAAI,CAAC,aAAa,CAAC,SAAS,KAAK,SAAS,EAC5C,CAAC;wBACC,OAAO,WAAW,GAAG,IAAI,CAAA;oBAC7B,CAAC;yBAAM,CAAC;wBACJ,OAAO,CACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;4BACpC,GAAG;4BACH,IAAI,CACP,CAAA;oBACL,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACJ,OAAO,IAAI,CAAA;gBACf,CAAC;YACL,CAAC,CAAC;iBACD,IAAI,CAAC,IAAI,CAAC,CAAA;YAEf,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBACnC,iBAAiB;oBACb,QAAQ;wBACR,OAAO;6BACF,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;4BACZ,OAAO,IAAI,CAAC,eAAe,CAAC;gCACxB,IAAI,EACA,MACH,CAAC,2BAA2B,CAAC,MAAM,CAAC,IAAI,CAAC;gCAC1C,GAAG,EAAG,MAAuB,CAAC,MAAM,CAAC,QAAQ;6BAChD,CAAC,CAAA;wBACN,CAAC,CAAC;6BACD,IAAI,CAAC,IAAI,CAAC,CAAA;YACvB,CAAC;YAED,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;gBAClC,IACI,IAAI,CAAC,aAAa,CAAC,SAAS,KAAK,QAAQ;oBACzC,IAAI,CAAC,aAAa,CAAC,SAAS,KAAK,QAAQ,EAC3C,CAAC;oBACC,iBAAiB,IAAI,oBAAoB,CAAA;gBAC7C,CAAC;YACL,CAAC;YAED,OAAO,iBAAiB,CAAA;QAC5B,CAAC;aAAM,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC,SAAS,KAAK,QAAQ,EAAE,CAAC;YAC1D,OAAO,IAAI,CAAC,aAAa,CAAC,SAAS,CAAA;QACvC,CAAC;QAED,OAAO,EAAE,CAAA;IACb,CAAC;IAED;;;OAGG;IACO,mBAAmB;QACzB,MAAM,OAAO,GAAqB,EAAE,CAAA;QACpC,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,EAAE,CAAC;YAC9C,CAAC;YAAC,IAAI,CAAC,aAAa,CAAC,SAAsB,CAAC,OAAO,CAC/C,CAAC,UAAU,EAAE,EAAE;gBACX,IAAI,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,WAAW,EAAE,CAAC;oBAC5C,OAAO,CAAC,IAAI,CACR,GAAG,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ,CAAC,2BAA2B,CACjE,UAAU,CACb,CACJ,CAAA;gBACL,CAAC;YACL,CAAC,CACJ,CAAA;QACL,CAAC;QACD,OAAO,OAAO,CAAA;IAClB,CAAC;IAES,4BAA4B,CAAC,OAAsB;QACzD,OAAO,OAAO;aACT,GAAG,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACnB,MAAM,UAAU,GAAG,IAAI,CAAC,8BAA8B,CAClD,MAAM,CAAC,SAAS,CACnB,CAAA;YAED,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;gBAClB,KAAK,KAAK;oBACN,OAAO,CACH,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;wBACzB,GACI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,sBAAsB;4BAC1C,CAAC,CAAC,GAAG;4BACL,CAAC,CAAC,EACV,GAAG,UAAU,GACT,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,sBAAsB;4BAC1C,CAAC,CAAC,GAAG;4BACL,CAAC,CAAC,EACV,EAAE,CACL,CAAA;gBACL,KAAK,IAAI;oBACL,OAAO,CACH,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;wBACxB,GACI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,sBAAsB;4BAC1C,CAAC,CAAC,GAAG;4BACL,CAAC,CAAC,EACV,GAAG,UAAU,GACT,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,sBAAsB;4BAC1C,CAAC,CAAC,GAAG;4BACL,CAAC,CAAC,EACV,EAAE,CACL,CAAA;YACT,CAAC;YAED,OAAO,UAAU,CAAA;QACrB,CAAC,CAAC;aACD,IAAI,CAAC,GAAG,CAAC;aACT,IAAI,EAAE,CAAA;IACf,CAAC;IAED;;OAEG;IACO,8BAA8B,CACpC,SAA+B,EAC/B,aAAsB,KAAK;QAE3B,IAAI,OAAO,SAAS,KAAK,QAAQ;YAAE,OAAO,SAAS,CAAA;QAEnD,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;YAC3B,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACzB,OAAO,KAAK,CAAA;YAChB,CAAC;YAED,gEAAgE;YAChE,yDAAyD;YACzD,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;gBACxC,OAAO,IAAI,CAAC,4BAA4B,CAAC,SAAS,CAAC,CAAA;YACvD,CAAC;YAED,OAAO,GAAG,GAAG,IAAI,CAAC,4BAA4B,CAAC,SAAS,CAAC,GAAG,GAAG,CAAA;QACnE,CAAC;QAED,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAA;QAElC,QAAQ,SAAS,CAAC,QAAQ,EAAE,CAAC;YACzB,KAAK,UAAU;gBACX,OAAO,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAA;YACpE,KAAK,iBAAiB;gBAClB,OAAO,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,OAAO,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAA;YACrE,KAAK,eAAe;gBAChB,OAAO,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,OAAO,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAA;YACrE,KAAK,cAAc;gBACf,OAAO,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,eAAe,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAA;YAC7E,KAAK,kBAAkB;gBACnB,OAAO,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,OAAO,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAA;YACrE,KAAK,cAAc;gBACf,OAAO,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,OAAO,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAA;YACrE,KAAK,UAAU;gBACX,OAAO,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAA;YACpE,KAAK,iBAAiB;gBAClB,OAAO,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,OAAO,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAA;YACrE,KAAK,UAAU;gBACX,OAAO,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,OAAO,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAA;YACrE,KAAK,OAAO;gBACR,OAAO,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAA;YACpE,KAAK,OAAO;gBACR,IACI,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,UAAU;oBAClC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,aAAa,EACvC,CAAC;oBACC,OAAO,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAA;gBACxE,CAAC;gBAED,OAAO,SAAS,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,gBAAgB,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAA;YACrF,KAAK,MAAM;gBACP,OAAO,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAA;YACvE,KAAK,SAAS;gBACV,OAAO,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAA;YACzG,KAAK,IAAI;gBACL,IAAI,SAAS,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;oBACnC,OAAO,KAAK,CAAA;gBAChB,CAAC;gBACD,OAAO,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ,SAAS,CAAC,UAAU;qBACxD,KAAK,CAAC,CAAC,CAAC;qBACR,IAAI,CAAC,IAAI,CAAC,GAAG,CAAA;YACtB,KAAK,KAAK;gBACN,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;oBACxC,OAAO,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,kBAAkB,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,aAAa,CAAA;gBAC3F,CAAC;gBAED,OAAO,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAA;YACzE,KAAK,QAAQ;gBACT,OAAO,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAA;YAE/C,KAAK,KAAK;gBACN,OAAO,OAAO,IAAI,CAAC,8BAA8B,CAC7C,SAAS,CAAC,SAAS,CACtB,GAAG,CAAA;YACR,KAAK,UAAU;gBACX,OAAO,GAAG,IAAI,CAAC,8BAA8B,CACzC,SAAS,CAAC,SAAS,EACnB,IAAI,CACP,EAAE,CAAA;YACP,KAAK,KAAK;gBACN,OAAO,GAAG,GAAG,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,CAAA;YACzD,KAAK,IAAI;gBACL,OAAO,GAAG,GAAG,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,CAAA;QAC5D,CAAC;QAED,MAAM,IAAI,SAAS,CACf,4BAA4B,2BAAY,CAAC,WAAW,CAAC,IAAI,EAAE,CAC9D,CAAA;IACL,CAAC;IAES,mBAAmB;QACzB,IAAI,CAAC,IAAI,CAAC,yBAAyB,EAAE,EAAE,CAAC;YACpC,OAAO,EAAE,CAAA;QACb,CAAC;QACD,MAAM,2BAA2B,GAC7B,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,eAAe,CAAC,qBAAqB,CAAA;QAEhE,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAAC,GAAG,CAC5D,CAAC,GAAG,EAAE,EAAE;YACJ,IAAI,iBAAiB,GACjB,OAAO,GAAG,CAAC,YAAY,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAA;YAChE,IAAI,OAAO,GAAG,CAAC,YAAY,KAAK,QAAQ,EAAE,CAAC;gBACvC,IAAI,GAAG,CAAC,YAAY,CAAC,yBAAyB,EAAE,EAAE,CAAC;oBAC/C,MAAM,IAAI,oBAAY,CAClB,sCAAsC,GAAG,CAAC,KAAK,GAAG,CACrD,CAAA;gBACL,CAAC;gBACD,iBAAiB,GAAG,GAAG,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAA;gBAC/C,IACI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,eAAe,CAAC,QAAQ;oBAChD,CAAC,iCAAe,CAAC,oBAAoB,CAAC,GAAG,CAAC,YAAY,CAAC,EACzD,CAAC;oBACC,MAAM,IAAI,oBAAY,CAClB,gDAAgD,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,UAAU,GAAG,CAAC,KAAK,GAAG,CACrG,CAAA;gBACL,CAAC;gBACD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,aAAa,EAAE,CAAC,CAAA;YACxD,CAAC;YACD,IAAI,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;YACtC,IAAI,GAAG,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;gBAC1B,MAAM,kBAAkB,GAAG,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAClD,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAClC,CAAA;gBACD,IACI,iCAAe,CAAC,oBAAoB,CAAC,GAAG,CAAC,YAAY,CAAC,EACxD,CAAC;oBACC,IACI,GAAG,CAAC,YAAY,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM;wBAC7C,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM;4BAC1B,GAAG,CAAC,YAAY,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,EACnD,CAAC;wBACC,MAAM,IAAI,oBAAY,CAClB,mCAAmC,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,+CAA+C,GAAG,CAAC,YAAY,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,UAAU,GAAG,CAAC,KAAK,GAAG,CACtL,CAAA;oBACL,CAAC;gBACL,CAAC;gBACD,SAAS,IAAI,IAAI,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAA;YACrD,CAAC;YACD,MAAM,eAAe,GACjB,GAAG,CAAC,OAAO,CAAC,SAAS,IAAI,2BAA2B;gBAChD,CAAC,CAAC,WAAW;gBACb,CAAC,CAAC,EAAE,CAAA;YACZ,IAAI,iBAAiB,GAAG,EAAE,CAAA;YAC1B,IACI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,eAAe,CAAC,gBAAgB;gBACvD,GAAG,CAAC,OAAO,CAAC,YAAY,KAAK,SAAS,EACxC,CAAC;gBACC,iBAAiB,GAAG,GAAG,CAAC,OAAO,CAAC,YAAY;oBACxC,CAAC,CAAC,cAAc;oBAChB,CAAC,CAAC,kBAAkB,CAAA;YAC5B,CAAC;YAED,OAAO;gBACH,eAAe;gBACf,SAAS;gBACT,IAAI;gBACJ,iBAAiB;gBACjB,IAAI,iBAAiB,GAAG;aAC3B;iBACI,MAAM,CAAC,OAAO,CAAC;iBACf,IAAI,CAAC,GAAG,CAAC,CAAA;QAClB,CAAC,CACJ,CAAA;QAED,OAAO,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,CAAA;IAChD,CAAC;IAED;;OAEG;IACO,sBAAsB,CAC5B,GAAgB;QAEhB,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ,CAAA;QACvD,MAAM,UAAU,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAC7D,QAAQ,CAAC,iBAAiB,CAAC,EAAE,CAAC,CACjC,CAAA;QAED,mDAAmD;QACnD,IAAI,CAAC,QAAQ,CAAC,sBAAsB,EAAE,CAAC;YACnC,MAAM,aAAa,GAAG,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,CAAA;YAEhD,yDAAyD;YACzD,2DAA2D;YAC3D,2EAA2E;YAC3E,IACI,CAAC,aAAa,CAAC,WAAW;gBAC1B,CAAC,aAAa,CAAC,gBAAgB;gBAC/B,CAAC,aAAa,CAAC,gBAAgB,EACjC,CAAC;gBACC,OAAO;oBACH,CAAC,aAAa,CAAC,YAAY,CAAC,EAAE,IAAA,OAAE,EAC5B,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAClB,aAAa,CAAC,cAAc,CAAC,EAAE,EAAE,KAAK,CAAC,CAC1C,CACJ;iBACJ,CAAA;YACL,CAAC;QACL,CAAC;QAED,OAAO,IAAI,mBAAQ,CAAC,CAAC,EAAE,EAAE,EAAE;YACvB,KAAK,MAAM,IAAI,IAAI,UAAU,EAAE,CAAC;gBAC5B,EAAE,CAAC,OAAO,CAAC,IAAI,mBAAQ,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;YACpD,CAAC;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAES,kBAAkB,CAAC,QAAa;QACtC,MAAM,KAAK,GAAG,QAAQ;aACjB,KAAK,EAAE;aACP,OAAO,EAAE;aACT,OAAO,EAAE;aACT,MAAM,CAAC,SAAS,CAAC;aACjB,KAAK,CAAC,SAAS,CAAC;aAChB,IAAI,CAAC,SAAS,CAAC;aACf,IAAI,CAAC,SAAS,CAAC;aACf,MAAM,CAAC,GAAG,CAAC;aACX,SAAS,CAAC,sBAAsB,CAAC,CAAA;QAEtC,OAAO,CAAC,WAAW,KAAK,CAAC,QAAQ,EAAE,GAAG,EAAE,KAAK,CAAC,aAAa,EAAE,CAAC,CAAA;IAClE,CAAC;IAEO,0BAA0B,CAC9B,YAAoB;QAEpB,mDAAmD;QACnD,+DAA+D;QAE/D,oDAAoD;QACpD,IAAI,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAA;QACxC,MAAM,IAAI,GAAa,EAAE,CAAA;QACzB,MAAM,iBAAiB,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAEjD,OAAO,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClC,MAAM,IAAI,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAA;YAEjC,IAAI,CAAC,KAAK,EAAE,WAAW,EAAE,CAAC;gBACtB,iDAAiD;gBACjD,0CAA0C;gBAC1C,MAAK;YACT,CAAC;YAED,IAAI,KAAK,CAAC,QAAQ,CAAC,2BAA2B,CAAC,IAAI,CAAC,EAAE,CAAC;gBACnD,+EAA+E;gBAC/E,yFAAyF;gBACzF,sBAAsB;gBACtB,iBAAiB,CAAC,OAAO,CACrB,GAAG,iBAAiB,CAAC,KAAK,EAAE,IAAI,iBAAiB,CAAC,KAAK,EAAE,EAAE,CAC9D,CAAA;gBACD,SAAQ;YACZ,CAAC;YAED,IAAI,KAAK,CAAC,QAAQ,CAAC,2BAA2B,CAAC,IAAI,CAAC,EAAE,CAAC;gBACnD,wDAAwD;gBACxD,uDAAuD;gBACvD,oBAAoB;gBACpB,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CACnD,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,oBAAoB,KAAK,IAAI,CACvD,CAAA;gBAED,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC;oBACnB,MAAM,gBAAgB,GAClB,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,CAAA;oBACxD,MAAM,IAAI,KAAK,CACX,qCAAqC,gBAAgB,EAAE,CAC1D,CAAA;gBACL,CAAC;gBAED,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAA;gBACtB,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAA;gBAC7B,iBAAiB,CAAC,KAAK,EAAE,CAAA;gBACzB,SAAQ;YACZ,CAAC;YAED,MAAK;QACT,CAAC;QAED,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,MAAM,IAAI,KAAK,CAAC,kCAAkC,YAAY,EAAE,CAAC,CAAA;QACrE,CAAC;QAED,8EAA8E;QAC9E,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAErD,MAAM,OAAO,GACT,KAAK,CAAC,QAAQ,CAAC,2BAA2B,CAAC,iBAAiB,CAAC,CAAA;QAEjE,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YAClB,MAAM,IAAI,yDAA2B,CAAC,YAAY,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAA;QACvE,CAAC;QAED,OAAO,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,CAAC,CAAA;IACjC,CAAC;IAED;;OAEG;IACO,kBAAkB,CACxB,QAAwB,EACxB,MAAqB,EACrB,SAAiB,EAAE;QAEnB,MAAM,KAAK,GAAa,EAAE,CAAA;QAE1B,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;YACpC,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAA;YAE9C,iEAAiE;YACjE,2FAA2F;YAC3F,IACI,MAAM,CAAC,GAAG,CAAC,KAAK,IAAI;gBACpB,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,QAAQ;gBAC/B,iCAAe,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAC7C,CAAC;gBACC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gBAChB,SAAQ;YACZ,CAAC;YAED,IAAI,QAAQ,CAAC,2BAA2B,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC7C,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CACpC,QAAQ,EACR,MAAM,CAAC,GAAG,CAAC,EACX,IAAI,CACP,CAAA;gBACD,KAAK,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAA;gBACvB,SAAQ;YACZ,CAAC;YAED,IAAI,QAAQ,CAAC,2BAA2B,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC7C,MAAM,QAAQ,GAAG,QAAQ,CAAC,4BAA4B,CAAC,IAAI,CAAE,CAAA;gBAE7D,+EAA+E;gBAC/E,qFAAqF;gBACrF,iDAAiD;gBAEjD,qFAAqF;gBACrF,gFAAgF;gBAChF,iEAAiE;gBACjE,IACI,QAAQ,CAAC,YAAY,KAAK,YAAY;oBACtC,QAAQ,CAAC,YAAY,KAAK,aAAa,EACzC,CAAC;oBACC,MAAM,WAAW,GAAG,QAAQ,CAAC,WAAW;yBACnC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,gBAAgB,CAAC;yBAC9B,MAAM,CAAC,CAAC,CAAC,EAAuB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;oBAE5C,MAAM,iBAAiB,GACnB,WAAW,CAAC,MAAM,GAAG,CAAC;wBACtB,WAAW,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,EAAE,CACzB,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAC5C,CAAA;oBAEL,IAAI,iBAAiB,EAAE,CAAC;wBACpB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;wBAChB,SAAQ;oBACZ,CAAC;gBACL,CAAC;gBAED,IACI,QAAQ,CAAC,YAAY,KAAK,aAAa;oBACvC,QAAQ,CAAC,YAAY,KAAK,cAAc,EAC1C,CAAC;oBACC,MAAM,IAAI,KAAK,CACX,uBAAuB,QAAQ,CAAC,YAAY,iBAAiB,IAAI,EAAE,CACtE,CAAA;gBACL,CAAC;gBAED,wFAAwF;gBACxF,oFAAoF;gBACpF,2BAA2B;gBAC3B,oFAAoF;gBACpF,gBAAgB;gBAChB,MAAM,cAAc,GAChB,QAAQ,CAAC,qBAAqB,CAAC,cAAc,CAAA;gBACjD,MAAM,iBAAiB,GACnB,cAAc,CAAC,MAAM,GAAG,CAAC;oBACzB,cAAc,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,EAAE,CAC5B,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAC5C,CAAA;gBAEL,IAAI,iBAAiB,EAAE,CAAC;oBACpB,MAAM,QAAQ,GAAG,cAAc,CAAC,GAAG,CAC/B,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,IAAI,IAAI,MAAM,CAAC,YAAY,EAAE,CAC/C,CAAA;oBACD,KAAK,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAA;oBACvB,SAAQ;gBACZ,CAAC;gBAED,yEAAyE;gBACzE,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CACpC,QAAQ,CAAC,qBAAqB,EAC9B,MAAM,CAAC,GAAG,CAAC,CACd,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE,CAAC,CAAA;gBAC5B,KAAK,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAA;gBACvB,SAAQ;YACZ,CAAC;YAED,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACpB,CAAC;QAED,OAAO,KAAK,CAAA;IAChB,CAAC;IAES,CAAC,aAAa,CAAC,KAAoB;QACzC,IAAI,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,WAAW,EAAE,CAAC;YAC5C,MAAM,aAAa,GAAG,IAAI,CAAC,kBAAkB,CACzC,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ,EACtC,KAAK,CACR,CAAA;YAED,KAAK,MAAM,YAAY,IAAI,aAAa,EAAE,CAAC;gBACvC,MAAM,CAAC,KAAK,EAAE,iBAAiB,EAAE,OAAO,CAAC,GACrC,IAAI,CAAC,0BAA0B,CAAC,YAAY,CAAC,CAAA;gBAEjD,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;oBAC3B,IAAI,cAAc,GAAG,KAAK,CAAA;oBAE1B,KAAK,MAAM,IAAI,IAAI,iBAAiB,EAAE,CAAC;wBACnC,IAAI,CAAC,cAAc,IAAI,CAAC,CAAC,IAAI,IAAI,cAAc,CAAC,EAAE,CAAC;4BAC/C,cAAc,GAAG,EAAE,CAAA;4BACnB,MAAK;wBACT,CAAC;wBAED,cAAc,GAAG,cAAc,CAAC,IAAI,CAAC,CAAA;oBACzC,CAAC;oBAED,4DAA4D;oBAC5D,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa;yBAC/B,yBAAyB;wBAC1B,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,IAAI,MAAM,CAAC,YAAY,EAAE;wBACxC,CAAC,CAAC,MAAM,CAAC,YAAY,CAAA;oBAEzB,MAAM,cAAc,GAAG,MAAM,CAAC,cAAc,CACxC,cAAc,EACd,IAAI,CACP,CAAA;oBAED,MAAM,CAAC,SAAS,EAAE,cAAc,CAAC,CAAA;gBACrC,CAAC;YACL,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gBACnC,MAAM,cAAc,GAAG,KAAK,CAAC,GAAG,CAAC,CAAA;gBACjC,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,yBAAyB;oBAC1D,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,IAAI,GAAG,EAAE;oBACxB,CAAC,CAAC,GAAG,CAAA;gBAET,MAAM,CAAC,SAAS,EAAE,cAAc,CAAC,CAAA;YACrC,CAAC;QACL,CAAC;IACL,CAAC;IAES,0BAA0B,CAChC,SAAiB,EACjB,cAAmB;QAEnB,IAAI,iCAAe,CAAC,cAAc,CAAC,cAAc,CAAC,EAAE,CAAC;YACjD,MAAM,UAAU,GAAU,EAAE,CAAA;YAC5B,IAAI,cAAc,CAAC,YAAY,EAAE,CAAC;gBAC9B,IAAI,cAAc,CAAC,uBAAuB,EAAE,CAAC;oBACzC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,uBAAuB,CAAC,CAAA;gBAC9D,CAAC;qBAAM,IAAI,cAAc,CAAC,kBAAkB,EAAE,CAAC;oBAC3C,KAAK,MAAM,CAAC,IAAI,cAAc,CAAC,KAAK,EAAE,CAAC;wBACnC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAA;oBAC5C,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACJ,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAA;gBAC/D,CAAC;YACL,CAAC;YAED,IAAI,cAAc,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC;gBAChC,IAAI,cAAc,CAAC,MAAM,EAAE,CAAC;oBACxB,OAAO,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;gBAC3C,CAAC;qBAAM,CAAC;oBACJ,OAAO;wBACH,QAAQ,EAAE,OAAO;wBACjB,UAAU,EAAE,CAAC,SAAS,EAAE,cAAc,CAAC,KAAK,CAAC;qBAChD,CAAA;gBACL,CAAC;YACL,CAAC;iBAAM,IAAI,cAAc,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC;gBACvC,IAAI,cAAc,CAAC,KAAK,EAAE,CAAC;oBACvB,OAAO;wBACH,QAAQ,EAAE,cAAc,CAAC,IAAI;wBAC7B,SAAS,EAAE,IAAI,CAAC,0BAA0B,CACtC,SAAS,EACT,cAAc,CAAC,KAAK,CACvB;qBACJ,CAAA;gBACL,CAAC;qBAAM,CAAC;oBACJ,OAAO;wBACH,QAAQ,EAAE,UAAU;wBACpB,UAAU,EAAE,CAAC,SAAS,EAAE,GAAG,UAAU,CAAC;qBACzC,CAAA;gBACL,CAAC;YACL,CAAC;iBAAM,IAAI,cAAc,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC;gBACvC,MAAM,MAAM,GAAwB,cAAc,CAAC,KAAK,CAAA;gBAExD,OAAO;oBACH,QAAQ,EAAE,cAAc,CAAC,IAAI;oBAC7B,UAAU,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAChC,IAAI,CAAC,8BAA8B,CAC/B,IAAI,CAAC,0BAA0B,CAC3B,SAAS,EACT,QAAQ,CACX,CACJ,CACJ;iBACJ,CAAA;YACL,CAAC;iBAAM,IAAI,cAAc,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;gBACtC,MAAM,MAAM,GAAwB,cAAc,CAAC,KAAK,CAAA;gBAExD,OAAO;oBACH,QAAQ,EAAE,cAAc,CAAC,IAAI;oBAC7B,UAAU,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAChC,IAAI,CAAC,8BAA8B,CAC/B,IAAI,CAAC,0BAA0B,CAC3B,SAAS,EACT,QAAQ,CACX,CACJ,CACJ;iBACJ,CAAA;YACL,CAAC;iBAAM,CAAC;gBACJ,OAAO;oBACH,QAAQ,EAAE,cAAc,CAAC,IAAI;oBAC7B,UAAU,EAAE,CAAC,SAAS,EAAE,GAAG,UAAU,CAAC;iBACzC,CAAA;YACL,CAAC;YACD,wCAAwC;YACxC,eAAe;YACf,8BAA8B;YAC9B,wBAAwB;YACxB,yBAAyB;YACzB,YAAY;YACZ,SAAS;QACb,CAAC;aAAM,CAAC;YACJ,OAAO;gBACH,QAAQ,EAAE,OAAO;gBACjB,UAAU,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;aAChE,CAAA;QACL,CAAC;IACL,CAAC;IAES,iBAAiB,CACvB,KAMqB;QAErB,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC5B,OAAO,KAAK,CAAA;QAChB,CAAC;QAED,IAAI,iCAAe,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;YACpC,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAA;YAEnD,iBAAiB,CAAC,kBAAkB,GAAG,IAAI,CAAA;YAE3C,iBAAiB,CAAC,aAAa,CAAC,SAAS;gBACrC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAA;YAChC,iBAAiB,CAAC,aAAa,CAAC,yBAAyB;gBACrD,IAAI,CAAC,aAAa,CAAC,yBAAyB,CAAA;YAChD,iBAAiB,CAAC,aAAa,CAAC,UAAU;gBACtC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAA;YACjC,iBAAiB,CAAC,aAAa,CAAC,gBAAgB;gBAC5C,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAA;YAEvC,iBAAiB,CAAC,aAAa,CAAC,MAAM,GAAG,EAAE,CAAA;YAE3C,KAAK,CAAC,YAAY,CAAC,iBAAwB,CAAC,CAAA;YAE5C,OAAO;gBACH,QAAQ,EAAE,iCAAe,CAAC,aAAa,CAAC,KAAK,CAAC;oBAC1C,CAAC,CAAC,KAAK;oBACP,CAAC,CAAC,UAAU;gBAChB,SAAS,EAAE,iBAAiB,CAAC,aAAa,CAAC,MAAM;aACpD,CAAA;QACL,CAAC;QAED,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE,CAAC;YAC9B,OAAO,KAAK,CAAC,IAAI,CAAC,CAAA;QACtB,CAAC;QAED,MAAM,MAAM,GAAoB,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAA;QACtE,MAAM,OAAO,GAAkB,EAAE,CAAA;QAEjC,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YACzB,MAAM,UAAU,GAAyB,EAAE,CAAA;YAE3C,wDAAwD;YACxD,KAAK,MAAM,CAAC,SAAS,EAAE,cAAc,CAAC,IAAI,IAAI,CAAC,aAAa,CACxD,KAAK,CACR,EAAE,CAAC;gBACA,UAAU,CAAC,IAAI,CAAC;oBACZ,IAAI,EAAE,KAAK;oBACX,SAAS,EAAE,IAAI,CAAC,0BAA0B,CACtC,SAAS,EACT,cAAc,CACjB;iBACJ,CAAC,CAAA;YACN,CAAC;YAED,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,CAAA;QACvD,CAAC;QAED,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvB,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;QAC/B,CAAC;QAED,OAAO,OAAO,CAAA;IAClB,CAAC;IAED;;OAEG;IACO,iBAAiB;QACvB,OAAO,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAA;IAClE,CAAC;IAES,yBAAyB;QAC/B,OAAO,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAAC,MAAM,GAAG,CAAC,CAAA;IAC/D,CAAC;;AAjmDL,oCAkmDC;AA9jDG;;GAEG;AACY,iCAAoB,GAAwB,EAAxB,AAA0B,CAAA", "file": "QueryBuilder.js", "sourcesContent": ["import { ObjectLiteral } from \"../common/ObjectLiteral\"\nimport { QueryRunner } from \"../query-runner/QueryRunner\"\nimport { DataSource } from \"../data-source/DataSource\"\nimport { QueryBuilderCteOptions } from \"./QueryBuilderCte\"\nimport { QueryExpressionMap } from \"./QueryExpressionMap\"\nimport { SelectQueryBuilder } from \"./SelectQueryBuilder\"\nimport { UpdateQueryBuilder } from \"./UpdateQueryBuilder\"\nimport { DeleteQueryBuilder } from \"./DeleteQueryBuilder\"\nimport { SoftDeleteQueryBuilder } from \"./SoftDeleteQueryBuilder\"\nimport { InsertQueryBuilder } from \"./InsertQueryBuilder\"\nimport { RelationQueryBuilder } from \"./RelationQueryBuilder\"\nimport { EntityTarget } from \"../common/EntityTarget\"\nimport { Alias } from \"./Alias\"\nimport { Brackets } from \"./Brackets\"\nimport { QueryDeepPartialEntity } from \"./QueryPartialEntity\"\nimport { EntityMetadata } from \"../metadata/EntityMetadata\"\nimport { ColumnMetadata } from \"../metadata/ColumnMetadata\"\nimport { FindOperator } from \"../find-options/FindOperator\"\nimport { In } from \"../find-options/operator/In\"\nimport { TypeORMError } from \"../error\"\nimport { WhereClause, WhereClauseCondition } from \"./WhereClause\"\nimport { NotBrackets } from \"./NotBrackets\"\nimport { EntityPropertyNotFoundError } from \"../error/EntityPropertyNotFoundError\"\nimport { ReturningType } from \"../driver/Driver\"\nimport { OracleDriver } from \"../driver/oracle/OracleDriver\"\nimport { InstanceChecker } from \"../util/InstanceChecker\"\nimport { escapeRegExp } from \"../util/escapeRegExp\"\n\n// todo: completely cover query builder with tests\n// todo: entityOrProperty can be target name. implement proper behaviour if it is.\n// todo: check in persistment if id exist on object and throw exception (can be in partial selection?)\n// todo: fix problem with long aliases eg getMaxIdentifierLength\n// todo: fix replacing in .select(\"COUNT(post.id) AS cnt\") statement\n// todo: implement joinAlways in relations and relationId\n// todo: finish partial selection\n// todo: sugar methods like: .addCount and .selectCount, selectCountAndMap, selectSum, selectSumAndMap, ...\n// todo: implement @Select decorator\n// todo: add select and map functions\n\n// todo: implement relation/entity loading and setting them into properties within a separate query\n// .loadAndMap(\"post.categories\", \"post.categories\", qb => ...)\n// .loadAndMap(\"post.categories\", Category, qb => ...)\n\n/**\n * Allows to build complex sql queries in a fashion way and execute those queries.\n */\nexport abstract class QueryBuilder<Entity extends ObjectLiteral> {\n    readonly \"@instanceof\" = Symbol.for(\"QueryBuilder\")\n\n    // -------------------------------------------------------------------------\n    // Public Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Connection on which QueryBuilder was created.\n     */\n    readonly connection: DataSource\n\n    /**\n     * Contains all properties of the QueryBuilder that needs to be build a final query.\n     */\n    readonly expressionMap: QueryExpressionMap\n\n    // -------------------------------------------------------------------------\n    // Protected Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Query runner used to execute query builder query.\n     */\n    protected queryRunner?: QueryRunner\n\n    /**\n     * If QueryBuilder was created in a subquery mode then its parent QueryBuilder (who created subquery) will be stored here.\n     */\n    protected parentQueryBuilder: QueryBuilder<any>\n\n    /**\n     * Memo to help keep place of current parameter index for `createParameter`\n     */\n    private parameterIndex = 0\n\n    /**\n     * Contains all registered query builder classes.\n     */\n    private static queryBuilderRegistry: Record<string, any> = {}\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    /**\n     * QueryBuilder can be initialized from given Connection and QueryRunner objects or from given other QueryBuilder.\n     */\n    constructor(queryBuilder: QueryBuilder<any>)\n\n    /**\n     * QueryBuilder can be initialized from given Connection and QueryRunner objects or from given other QueryBuilder.\n     */\n    constructor(connection: DataSource, queryRunner?: QueryRunner)\n\n    /**\n     * QueryBuilder can be initialized from given Connection and QueryRunner objects or from given other QueryBuilder.\n     */\n    constructor(\n        connectionOrQueryBuilder: DataSource | QueryBuilder<any>,\n        queryRunner?: QueryRunner,\n    ) {\n        if (InstanceChecker.isDataSource(connectionOrQueryBuilder)) {\n            this.connection = connectionOrQueryBuilder\n            this.queryRunner = queryRunner\n            this.expressionMap = new QueryExpressionMap(this.connection)\n        } else {\n            this.connection = connectionOrQueryBuilder.connection\n            this.queryRunner = connectionOrQueryBuilder.queryRunner\n            this.expressionMap = connectionOrQueryBuilder.expressionMap.clone()\n        }\n    }\n\n    static registerQueryBuilderClass(name: string, factory: any) {\n        QueryBuilder.queryBuilderRegistry[name] = factory\n    }\n\n    // -------------------------------------------------------------------------\n    // Abstract Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Gets generated SQL query without parameters being replaced.\n     */\n    abstract getQuery(): string\n\n    // -------------------------------------------------------------------------\n    // Accessors\n    // -------------------------------------------------------------------------\n\n    /**\n     * Gets the main alias string used in this query builder.\n     */\n    get alias(): string {\n        if (!this.expressionMap.mainAlias)\n            throw new TypeORMError(`Main alias is not set`) // todo: better exception\n\n        return this.expressionMap.mainAlias.name\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates SELECT query.\n     * Replaces all previous selections if they exist.\n     */\n    select(): SelectQueryBuilder<Entity>\n\n    /**\n     * Creates SELECT query and selects given data.\n     * Replaces all previous selections if they exist.\n     */\n    select(\n        selection: string,\n        selectionAliasName?: string,\n    ): SelectQueryBuilder<Entity>\n\n    /**\n     * Creates SELECT query and selects given data.\n     * Replaces all previous selections if they exist.\n     */\n    select(selection: string[]): SelectQueryBuilder<Entity>\n\n    /**\n     * Creates SELECT query and selects given data.\n     * Replaces all previous selections if they exist.\n     */\n    select(\n        selection?: string | string[],\n        selectionAliasName?: string,\n    ): SelectQueryBuilder<Entity> {\n        this.expressionMap.queryType = \"select\"\n        if (Array.isArray(selection)) {\n            this.expressionMap.selects = selection.map((selection) => ({\n                selection: selection,\n            }))\n        } else if (selection) {\n            this.expressionMap.selects = [\n                { selection: selection, aliasName: selectionAliasName },\n            ]\n        }\n\n        if (InstanceChecker.isSelectQueryBuilder(this)) return this as any\n\n        return QueryBuilder.queryBuilderRegistry[\"SelectQueryBuilder\"](this)\n    }\n\n    /**\n     * Creates INSERT query.\n     */\n    insert(): InsertQueryBuilder<Entity> {\n        this.expressionMap.queryType = \"insert\"\n\n        if (InstanceChecker.isInsertQueryBuilder(this)) return this as any\n\n        return QueryBuilder.queryBuilderRegistry[\"InsertQueryBuilder\"](this)\n    }\n\n    /**\n     * Creates UPDATE query and applies given update values.\n     */\n    update(): UpdateQueryBuilder<Entity>\n\n    /**\n     * Creates UPDATE query and applies given update values.\n     */\n    update(\n        updateSet: QueryDeepPartialEntity<Entity>,\n    ): UpdateQueryBuilder<Entity>\n\n    /**\n     * Creates UPDATE query for the given entity and applies given update values.\n     */\n    update<Entity extends ObjectLiteral>(\n        entity: EntityTarget<Entity>,\n        updateSet?: QueryDeepPartialEntity<Entity>,\n    ): UpdateQueryBuilder<Entity>\n\n    /**\n     * Creates UPDATE query for the given table name and applies given update values.\n     */\n    update(\n        tableName: string,\n        updateSet?: QueryDeepPartialEntity<Entity>,\n    ): UpdateQueryBuilder<Entity>\n\n    /**\n     * Creates UPDATE query and applies given update values.\n     */\n    update(\n        entityOrTableNameUpdateSet?: EntityTarget<any> | ObjectLiteral,\n        maybeUpdateSet?: ObjectLiteral,\n    ): UpdateQueryBuilder<any> {\n        const updateSet = maybeUpdateSet\n            ? maybeUpdateSet\n            : (entityOrTableNameUpdateSet as ObjectLiteral | undefined)\n        entityOrTableNameUpdateSet = InstanceChecker.isEntitySchema(\n            entityOrTableNameUpdateSet,\n        )\n            ? entityOrTableNameUpdateSet.options.name\n            : entityOrTableNameUpdateSet\n\n        if (\n            typeof entityOrTableNameUpdateSet === \"function\" ||\n            typeof entityOrTableNameUpdateSet === \"string\"\n        ) {\n            const mainAlias = this.createFromAlias(entityOrTableNameUpdateSet)\n            this.expressionMap.setMainAlias(mainAlias)\n        }\n\n        this.expressionMap.queryType = \"update\"\n        this.expressionMap.valuesSet = updateSet\n\n        if (InstanceChecker.isUpdateQueryBuilder(this)) return this as any\n\n        return QueryBuilder.queryBuilderRegistry[\"UpdateQueryBuilder\"](this)\n    }\n\n    /**\n     * Creates DELETE query.\n     */\n    delete(): DeleteQueryBuilder<Entity> {\n        this.expressionMap.queryType = \"delete\"\n\n        if (InstanceChecker.isDeleteQueryBuilder(this)) return this as any\n\n        return QueryBuilder.queryBuilderRegistry[\"DeleteQueryBuilder\"](this)\n    }\n\n    softDelete(): SoftDeleteQueryBuilder<any> {\n        this.expressionMap.queryType = \"soft-delete\"\n\n        if (InstanceChecker.isSoftDeleteQueryBuilder(this)) return this as any\n\n        return QueryBuilder.queryBuilderRegistry[\"SoftDeleteQueryBuilder\"](this)\n    }\n\n    restore(): SoftDeleteQueryBuilder<any> {\n        this.expressionMap.queryType = \"restore\"\n\n        if (InstanceChecker.isSoftDeleteQueryBuilder(this)) return this as any\n\n        return QueryBuilder.queryBuilderRegistry[\"SoftDeleteQueryBuilder\"](this)\n    }\n\n    /**\n     * Sets entity's relation with which this query builder gonna work.\n     */\n    relation(propertyPath: string): RelationQueryBuilder<Entity>\n\n    /**\n     * Sets entity's relation with which this query builder gonna work.\n     */\n    relation<T extends ObjectLiteral>(\n        entityTarget: EntityTarget<T>,\n        propertyPath: string,\n    ): RelationQueryBuilder<T>\n\n    /**\n     * Sets entity's relation with which this query builder gonna work.\n     */\n    relation(\n        entityTargetOrPropertyPath: Function | string,\n        maybePropertyPath?: string,\n    ): RelationQueryBuilder<Entity> {\n        const entityTarget =\n            arguments.length === 2 ? entityTargetOrPropertyPath : undefined\n        const propertyPath =\n            arguments.length === 2\n                ? (maybePropertyPath as string)\n                : (entityTargetOrPropertyPath as string)\n\n        this.expressionMap.queryType = \"relation\"\n        this.expressionMap.relationPropertyPath = propertyPath\n\n        if (entityTarget) {\n            const mainAlias = this.createFromAlias(entityTarget)\n            this.expressionMap.setMainAlias(mainAlias)\n        }\n\n        if (InstanceChecker.isRelationQueryBuilder(this)) return this as any\n\n        return QueryBuilder.queryBuilderRegistry[\"RelationQueryBuilder\"](this)\n    }\n\n    /**\n     * Checks if given relation exists in the entity.\n     * Returns true if relation exists, false otherwise.\n     *\n     * todo: move this method to manager? or create a shortcut?\n     */\n    hasRelation<T>(target: EntityTarget<T>, relation: string): boolean\n\n    /**\n     * Checks if given relations exist in the entity.\n     * Returns true if relation exists, false otherwise.\n     *\n     * todo: move this method to manager? or create a shortcut?\n     */\n    hasRelation<T>(target: EntityTarget<T>, relation: string[]): boolean\n\n    /**\n     * Checks if given relation or relations exist in the entity.\n     * Returns true if relation exists, false otherwise.\n     *\n     * todo: move this method to manager? or create a shortcut?\n     */\n    hasRelation<T>(\n        target: EntityTarget<T>,\n        relation: string | string[],\n    ): boolean {\n        const entityMetadata = this.connection.getMetadata(target)\n        const relations = Array.isArray(relation) ? relation : [relation]\n        return relations.every((relation) => {\n            return !!entityMetadata.findRelationWithPropertyPath(relation)\n        })\n    }\n\n    /**\n     * Check the existence of a parameter for this query builder.\n     */\n    hasParameter(key: string): boolean {\n        return (\n            this.parentQueryBuilder?.hasParameter(key) ||\n            key in this.expressionMap.parameters\n        )\n    }\n\n    /**\n     * Sets parameter name and its value.\n     *\n     * The key for this parameter may contain numbers, letters, underscores, or periods.\n     */\n    setParameter(key: string, value: any): this {\n        if (typeof value === \"function\") {\n            throw new TypeORMError(\n                `Function parameter isn't supported in the parameters. Please check \"${key}\" parameter.`,\n            )\n        }\n\n        if (!key.match(/^([A-Za-z0-9_.]+)$/)) {\n            throw new TypeORMError(\n                \"QueryBuilder parameter keys may only contain numbers, letters, underscores, or periods.\",\n            )\n        }\n\n        if (this.parentQueryBuilder) {\n            this.parentQueryBuilder.setParameter(key, value)\n        }\n\n        this.expressionMap.parameters[key] = value\n        return this\n    }\n\n    /**\n     * Adds all parameters from the given object.\n     */\n    setParameters(parameters: ObjectLiteral): this {\n        for (const [key, value] of Object.entries(parameters)) {\n            this.setParameter(key, value)\n        }\n\n        return this\n    }\n\n    protected createParameter(value: any): string {\n        let parameterName\n\n        do {\n            parameterName = `orm_param_${this.parameterIndex++}`\n        } while (this.hasParameter(parameterName))\n\n        this.setParameter(parameterName, value)\n\n        return `:${parameterName}`\n    }\n\n    /**\n     * Adds native parameters from the given object.\n     *\n     * @deprecated Use `setParameters` instead\n     */\n    setNativeParameters(parameters: ObjectLiteral): this {\n        // set parent query builder parameters as well in sub-query mode\n        if (this.parentQueryBuilder) {\n            this.parentQueryBuilder.setNativeParameters(parameters)\n        }\n\n        Object.keys(parameters).forEach((key) => {\n            this.expressionMap.nativeParameters[key] = parameters[key]\n        })\n        return this\n    }\n\n    /**\n     * Gets all parameters.\n     */\n    getParameters(): ObjectLiteral {\n        const parameters: ObjectLiteral = Object.assign(\n            {},\n            this.expressionMap.parameters,\n        )\n\n        // add discriminator column parameter if it exist\n        if (\n            this.expressionMap.mainAlias &&\n            this.expressionMap.mainAlias.hasMetadata\n        ) {\n            const metadata = this.expressionMap.mainAlias!.metadata\n            if (metadata.discriminatorColumn && metadata.parentEntityMetadata) {\n                const values = metadata.childEntityMetadatas\n                    .filter(\n                        (childMetadata) => childMetadata.discriminatorColumn,\n                    )\n                    .map((childMetadata) => childMetadata.discriminatorValue)\n                values.push(metadata.discriminatorValue)\n                parameters[\"discriminatorColumnValues\"] = values\n            }\n        }\n\n        return parameters\n    }\n\n    /**\n     * Prints sql to stdout using console.log.\n     */\n    printSql(): this {\n        // TODO rename to logSql()\n        const [query, parameters] = this.getQueryAndParameters()\n        this.connection.logger.logQuery(query, parameters)\n        return this\n    }\n\n    /**\n     * Gets generated sql that will be executed.\n     * Parameters in the query are escaped for the currently used driver.\n     */\n    getSql(): string {\n        return this.getQueryAndParameters()[0]\n    }\n\n    /**\n     * Gets query to be executed with all parameters used in it.\n     */\n    getQueryAndParameters(): [string, any[]] {\n        // this execution order is important because getQuery method generates this.expressionMap.nativeParameters values\n        const query = this.getQuery()\n        const parameters = this.getParameters()\n        return this.connection.driver.escapeQueryWithParameters(\n            query,\n            parameters,\n            this.expressionMap.nativeParameters,\n        )\n    }\n\n    /**\n     * Executes sql generated by query builder and returns raw database results.\n     */\n    async execute(): Promise<any> {\n        const [sql, parameters] = this.getQueryAndParameters()\n        const queryRunner = this.obtainQueryRunner()\n        try {\n            return await queryRunner.query(sql, parameters) // await is needed here because we are using finally\n        } finally {\n            if (queryRunner !== this.queryRunner) {\n                // means we created our own query runner\n                await queryRunner.release()\n            }\n        }\n    }\n\n    /**\n     * Creates a completely new query builder.\n     * Uses same query runner as current QueryBuilder.\n     */\n    createQueryBuilder(queryRunner?: QueryRunner): this {\n        return new (this.constructor as any)(\n            this.connection,\n            queryRunner ?? this.queryRunner,\n        )\n    }\n\n    /**\n     * Clones query builder as it is.\n     * Note: it uses new query runner, if you want query builder that uses exactly same query runner,\n     * you can create query builder using its constructor, for example new SelectQueryBuilder(queryBuilder)\n     * where queryBuilder is cloned QueryBuilder.\n     */\n    clone(): this {\n        return new (this.constructor as any)(this)\n    }\n\n    /**\n     * Includes a Query comment in the query builder.  This is helpful for debugging purposes,\n     * such as finding a specific query in the database server's logs, or for categorization using\n     * an APM product.\n     */\n    comment(comment: string): this {\n        this.expressionMap.comment = comment\n        return this\n    }\n\n    /**\n     * Disables escaping.\n     */\n    disableEscaping(): this {\n        this.expressionMap.disableEscaping = false\n        return this\n    }\n\n    /**\n     * Escapes table name, column name or alias name using current database's escaping character.\n     */\n    escape(name: string): string {\n        if (!this.expressionMap.disableEscaping) return name\n        return this.connection.driver.escape(name)\n    }\n\n    /**\n     * Sets or overrides query builder's QueryRunner.\n     */\n    setQueryRunner(queryRunner: QueryRunner): this {\n        this.queryRunner = queryRunner\n        return this\n    }\n\n    /**\n     * Indicates if listeners and subscribers must be called before and after query execution.\n     * Enabled by default.\n     */\n    callListeners(enabled: boolean): this {\n        this.expressionMap.callListeners = enabled\n        return this\n    }\n\n    /**\n     * If set to true the query will be wrapped into a transaction.\n     */\n    useTransaction(enabled: boolean): this {\n        this.expressionMap.useTransaction = enabled\n        return this\n    }\n\n    /**\n     * Adds CTE to query\n     */\n    addCommonTableExpression(\n        queryBuilder: QueryBuilder<any> | string,\n        alias: string,\n        options?: QueryBuilderCteOptions,\n    ): this {\n        this.expressionMap.commonTableExpressions.push({\n            queryBuilder,\n            alias,\n            options: options || {},\n        })\n        return this\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Gets escaped table name with schema name if SqlServer driver used with custom\n     * schema name, otherwise returns escaped table name.\n     */\n    protected getTableName(tablePath: string): string {\n        return tablePath\n            .split(\".\")\n            .map((i) => {\n                // this condition need because in SQL Server driver when custom database name was specified and schema name was not, we got `dbName..tableName` string, and doesn't need to escape middle empty string\n                if (i === \"\") return i\n                return this.escape(i)\n            })\n            .join(\".\")\n    }\n\n    /**\n     * Gets name of the table where insert should be performed.\n     */\n    protected getMainTableName(): string {\n        if (!this.expressionMap.mainAlias)\n            throw new TypeORMError(\n                `Entity where values should be inserted is not specified. Call \"qb.into(entity)\" method to specify it.`,\n            )\n\n        if (this.expressionMap.mainAlias.hasMetadata)\n            return this.expressionMap.mainAlias.metadata.tablePath\n\n        return this.expressionMap.mainAlias.tablePath!\n    }\n\n    /**\n     * Specifies FROM which entity's table select/update/delete will be executed.\n     * Also sets a main string alias of the selection data.\n     */\n    protected createFromAlias(\n        entityTarget:\n            | EntityTarget<any>\n            | ((qb: SelectQueryBuilder<any>) => SelectQueryBuilder<any>),\n        aliasName?: string,\n    ): Alias {\n        // if table has a metadata then find it to properly escape its properties\n        // const metadata = this.connection.entityMetadatas.find(metadata => metadata.tableName === tableName);\n        if (this.connection.hasMetadata(entityTarget)) {\n            const metadata = this.connection.getMetadata(entityTarget)\n\n            return this.expressionMap.createAlias({\n                type: \"from\",\n                name: aliasName,\n                metadata: this.connection.getMetadata(entityTarget),\n                tablePath: metadata.tablePath,\n            })\n        } else {\n            if (typeof entityTarget === \"string\") {\n                const isSubquery =\n                    entityTarget.substr(0, 1) === \"(\" &&\n                    entityTarget.substr(-1) === \")\"\n\n                return this.expressionMap.createAlias({\n                    type: \"from\",\n                    name: aliasName,\n                    tablePath: !isSubquery\n                        ? (entityTarget as string)\n                        : undefined,\n                    subQuery: isSubquery ? entityTarget : undefined,\n                })\n            }\n\n            const subQueryBuilder: SelectQueryBuilder<any> = (\n                entityTarget as any\n            )((this as any as SelectQueryBuilder<any>).subQuery())\n            this.setParameters(subQueryBuilder.getParameters())\n            const subquery = subQueryBuilder.getQuery()\n\n            return this.expressionMap.createAlias({\n                type: \"from\",\n                name: aliasName,\n                subQuery: subquery,\n            })\n        }\n    }\n\n    /**\n     * @deprecated this way of replace property names is too slow.\n     *  Instead, we'll replace property names at the end - once query is build.\n     */\n    protected replacePropertyNames(statement: string) {\n        return statement\n    }\n\n    /**\n     * Replaces all entity's propertyName to name in the given SQL string.\n     */\n    protected replacePropertyNamesForTheWholeQuery(statement: string) {\n        const replacements: { [key: string]: { [key: string]: string } } = {}\n\n        for (const alias of this.expressionMap.aliases) {\n            if (!alias.hasMetadata) continue\n            const replaceAliasNamePrefix =\n                this.expressionMap.aliasNamePrefixingEnabled && alias.name\n                    ? `${alias.name}.`\n                    : \"\"\n\n            if (!replacements[replaceAliasNamePrefix]) {\n                replacements[replaceAliasNamePrefix] = {}\n            }\n\n            // Insert & overwrite the replacements from least to most relevant in our replacements object.\n            // To do this we iterate and overwrite in the order of relevance.\n            // Least to Most Relevant:\n            // * Relation Property Path to first join column key\n            // * Relation Property Path + Column Path\n            // * Column Database Name\n            // * Column Property Name\n            // * Column Property Path\n\n            for (const relation of alias.metadata.relations) {\n                if (relation.joinColumns.length > 0)\n                    replacements[replaceAliasNamePrefix][\n                        relation.propertyPath\n                    ] = relation.joinColumns[0].databaseName\n            }\n\n            for (const relation of alias.metadata.relations) {\n                const allColumns = [\n                    ...relation.joinColumns,\n                    ...relation.inverseJoinColumns,\n                ]\n                for (const joinColumn of allColumns) {\n                    const propertyKey = `${relation.propertyPath}.${\n                        joinColumn.referencedColumn!.propertyPath\n                    }`\n                    replacements[replaceAliasNamePrefix][propertyKey] =\n                        joinColumn.databaseName\n                }\n            }\n\n            for (const column of alias.metadata.columns) {\n                replacements[replaceAliasNamePrefix][column.databaseName] =\n                    column.databaseName\n            }\n\n            for (const column of alias.metadata.columns) {\n                replacements[replaceAliasNamePrefix][column.propertyName] =\n                    column.databaseName\n            }\n\n            for (const column of alias.metadata.columns) {\n                replacements[replaceAliasNamePrefix][column.propertyPath] =\n                    column.databaseName\n            }\n        }\n\n        const replacementKeys = Object.keys(replacements)\n        const replaceAliasNamePrefixes = replacementKeys\n            .map((key) => escapeRegExp(key))\n            .join(\"|\")\n\n        if (replacementKeys.length > 0) {\n            statement = statement.replace(\n                new RegExp(\n                    // Avoid a lookbehind here since it's not well supported\n                    `([ =(]|^.{0})` + // any of ' =(' or start of line\n                        // followed by our prefix, e.g. 'tablename.' or ''\n                        `${\n                            replaceAliasNamePrefixes\n                                ? \"(\" + replaceAliasNamePrefixes + \")\"\n                                : \"\"\n                        }([^ =(),]+)` + // a possible property name: sequence of anything but ' =(),'\n                        // terminated by ' =),' or end of line\n                        `(?=[ =),]|.{0}$)`,\n                    \"gm\",\n                ),\n                (...matches) => {\n                    let match: string, pre: string, p: string\n                    if (replaceAliasNamePrefixes) {\n                        match = matches[0]\n                        pre = matches[1]\n                        p = matches[3]\n\n                        if (replacements[matches[2]][p]) {\n                            return `${pre}${this.escape(\n                                matches[2].substring(0, matches[2].length - 1),\n                            )}.${this.escape(replacements[matches[2]][p])}`\n                        }\n                    } else {\n                        match = matches[0]\n                        pre = matches[1]\n                        p = matches[2]\n\n                        if (replacements[\"\"][p]) {\n                            return `${pre}${this.escape(replacements[\"\"][p])}`\n                        }\n                    }\n                    return match\n                },\n            )\n        }\n\n        return statement\n    }\n\n    protected createComment(): string {\n        if (!this.expressionMap.comment) {\n            return \"\"\n        }\n\n        // ANSI SQL 2003 support C style comments - comments that start with `/*` and end with `*/`\n        // In some dialects query nesting is available - but not all.  Because of this, we'll need\n        // to scrub \"ending\" characters from the SQL but otherwise we can leave everything else\n        // as-is and it should be valid.\n\n        return `/* ${this.expressionMap.comment.replace(/\\*\\//g, \"\")} */ `\n    }\n\n    /**\n     * Time travel queries for CockroachDB\n     */\n    protected createTimeTravelQuery(): string {\n        if (\n            this.expressionMap.queryType === \"select\" &&\n            this.expressionMap.timeTravel\n        ) {\n            return ` AS OF SYSTEM TIME ${this.expressionMap.timeTravel}`\n        }\n\n        return \"\"\n    }\n\n    /**\n     * Creates \"WHERE\" expression.\n     */\n    protected createWhereExpression() {\n        const conditionsArray = []\n\n        const whereExpression = this.createWhereClausesExpression(\n            this.expressionMap.wheres,\n        )\n\n        if (whereExpression.length > 0 && whereExpression !== \"1=1\") {\n            conditionsArray.push(this.replacePropertyNames(whereExpression))\n        }\n\n        if (this.expressionMap.mainAlias!.hasMetadata) {\n            const metadata = this.expressionMap.mainAlias!.metadata\n            // Adds the global condition of \"non-deleted\" for the entity with delete date columns in select query.\n            if (\n                this.expressionMap.queryType === \"select\" &&\n                !this.expressionMap.withDeleted &&\n                metadata.deleteDateColumn\n            ) {\n                const column = this.expressionMap.aliasNamePrefixingEnabled\n                    ? this.expressionMap.mainAlias!.name +\n                      \".\" +\n                      metadata.deleteDateColumn.propertyName\n                    : metadata.deleteDateColumn.propertyName\n\n                const condition = `${this.replacePropertyNames(column)} IS NULL`\n                conditionsArray.push(condition)\n            }\n\n            if (metadata.discriminatorColumn && metadata.parentEntityMetadata) {\n                const column = this.expressionMap.aliasNamePrefixingEnabled\n                    ? this.expressionMap.mainAlias!.name +\n                      \".\" +\n                      metadata.discriminatorColumn.databaseName\n                    : metadata.discriminatorColumn.databaseName\n\n                const condition = `${this.replacePropertyNames(\n                    column,\n                )} IN (:...discriminatorColumnValues)`\n                conditionsArray.push(condition)\n            }\n        }\n\n        if (this.expressionMap.extraAppendedAndWhereCondition) {\n            const condition = this.replacePropertyNames(\n                this.expressionMap.extraAppendedAndWhereCondition,\n            )\n            conditionsArray.push(condition)\n        }\n\n        let condition = \"\"\n\n        // time travel\n        condition += this.createTimeTravelQuery()\n\n        if (!conditionsArray.length) {\n            condition += \"\"\n        } else if (conditionsArray.length === 1) {\n            condition += ` WHERE ${conditionsArray[0]}`\n        } else {\n            condition += ` WHERE ( ${conditionsArray.join(\" ) AND ( \")} )`\n        }\n\n        return condition\n    }\n\n    /**\n     * Creates \"RETURNING\" / \"OUTPUT\" expression.\n     */\n    protected createReturningExpression(returningType: ReturningType): string {\n        const columns = this.getReturningColumns()\n        const driver = this.connection.driver\n\n        // also add columns we must auto-return to perform entity updation\n        // if user gave his own returning\n        if (\n            typeof this.expressionMap.returning !== \"string\" &&\n            this.expressionMap.extraReturningColumns.length > 0 &&\n            driver.isReturningSqlSupported(returningType)\n        ) {\n            columns.push(\n                ...this.expressionMap.extraReturningColumns.filter((column) => {\n                    return columns.indexOf(column) === -1\n                }),\n            )\n        }\n\n        if (columns.length) {\n            let columnsExpression = columns\n                .map((column) => {\n                    const name = this.escape(column.databaseName)\n                    if (driver.options.type === \"mssql\") {\n                        if (\n                            this.expressionMap.queryType === \"insert\" ||\n                            this.expressionMap.queryType === \"update\" ||\n                            this.expressionMap.queryType === \"soft-delete\" ||\n                            this.expressionMap.queryType === \"restore\"\n                        ) {\n                            return \"INSERTED.\" + name\n                        } else {\n                            return (\n                                this.escape(this.getMainTableName()) +\n                                \".\" +\n                                name\n                            )\n                        }\n                    } else {\n                        return name\n                    }\n                })\n                .join(\", \")\n\n            if (driver.options.type === \"oracle\") {\n                columnsExpression +=\n                    \" INTO \" +\n                    columns\n                        .map((column) => {\n                            return this.createParameter({\n                                type: (\n                                    driver as OracleDriver\n                                ).columnTypeToNativeParameter(column.type),\n                                dir: (driver as OracleDriver).oracle.BIND_OUT,\n                            })\n                        })\n                        .join(\", \")\n            }\n\n            if (driver.options.type === \"mssql\") {\n                if (\n                    this.expressionMap.queryType === \"insert\" ||\n                    this.expressionMap.queryType === \"update\"\n                ) {\n                    columnsExpression += \" INTO @OutputTable\"\n                }\n            }\n\n            return columnsExpression\n        } else if (typeof this.expressionMap.returning === \"string\") {\n            return this.expressionMap.returning\n        }\n\n        return \"\"\n    }\n\n    /**\n     * If returning / output cause is set to array of column names,\n     * then this method will return all column metadatas of those column names.\n     */\n    protected getReturningColumns(): ColumnMetadata[] {\n        const columns: ColumnMetadata[] = []\n        if (Array.isArray(this.expressionMap.returning)) {\n            ;(this.expressionMap.returning as string[]).forEach(\n                (columnName) => {\n                    if (this.expressionMap.mainAlias!.hasMetadata) {\n                        columns.push(\n                            ...this.expressionMap.mainAlias!.metadata.findColumnsWithPropertyPath(\n                                columnName,\n                            ),\n                        )\n                    }\n                },\n            )\n        }\n        return columns\n    }\n\n    protected createWhereClausesExpression(clauses: WhereClause[]): string {\n        return clauses\n            .map((clause, index) => {\n                const expression = this.createWhereConditionExpression(\n                    clause.condition,\n                )\n\n                switch (clause.type) {\n                    case \"and\":\n                        return (\n                            (index > 0 ? \"AND \" : \"\") +\n                            `${\n                                this.connection.options.isolateWhereStatements\n                                    ? \"(\"\n                                    : \"\"\n                            }${expression}${\n                                this.connection.options.isolateWhereStatements\n                                    ? \")\"\n                                    : \"\"\n                            }`\n                        )\n                    case \"or\":\n                        return (\n                            (index > 0 ? \"OR \" : \"\") +\n                            `${\n                                this.connection.options.isolateWhereStatements\n                                    ? \"(\"\n                                    : \"\"\n                            }${expression}${\n                                this.connection.options.isolateWhereStatements\n                                    ? \")\"\n                                    : \"\"\n                            }`\n                        )\n                }\n\n                return expression\n            })\n            .join(\" \")\n            .trim()\n    }\n\n    /**\n     * Computes given where argument - transforms to a where string all forms it can take.\n     */\n    protected createWhereConditionExpression(\n        condition: WhereClauseCondition,\n        alwaysWrap: boolean = false,\n    ): string {\n        if (typeof condition === \"string\") return condition\n\n        if (Array.isArray(condition)) {\n            if (condition.length === 0) {\n                return \"1=1\"\n            }\n\n            // In the future we should probably remove this entire condition\n            // but for now to prevent any breaking changes it exists.\n            if (condition.length === 1 && !alwaysWrap) {\n                return this.createWhereClausesExpression(condition)\n            }\n\n            return \"(\" + this.createWhereClausesExpression(condition) + \")\"\n        }\n\n        const { driver } = this.connection\n\n        switch (condition.operator) {\n            case \"lessThan\":\n                return `${condition.parameters[0]} < ${condition.parameters[1]}`\n            case \"lessThanOrEqual\":\n                return `${condition.parameters[0]} <= ${condition.parameters[1]}`\n            case \"arrayContains\":\n                return `${condition.parameters[0]} @> ${condition.parameters[1]}`\n            case \"jsonContains\":\n                return `${condition.parameters[0]} ::jsonb @> ${condition.parameters[1]}`\n            case \"arrayContainedBy\":\n                return `${condition.parameters[0]} <@ ${condition.parameters[1]}`\n            case \"arrayOverlap\":\n                return `${condition.parameters[0]} && ${condition.parameters[1]}`\n            case \"moreThan\":\n                return `${condition.parameters[0]} > ${condition.parameters[1]}`\n            case \"moreThanOrEqual\":\n                return `${condition.parameters[0]} >= ${condition.parameters[1]}`\n            case \"notEqual\":\n                return `${condition.parameters[0]} != ${condition.parameters[1]}`\n            case \"equal\":\n                return `${condition.parameters[0]} = ${condition.parameters[1]}`\n            case \"ilike\":\n                if (\n                    driver.options.type === \"postgres\" ||\n                    driver.options.type === \"cockroachdb\"\n                ) {\n                    return `${condition.parameters[0]} ILIKE ${condition.parameters[1]}`\n                }\n\n                return `UPPER(${condition.parameters[0]}) LIKE UPPER(${condition.parameters[1]})`\n            case \"like\":\n                return `${condition.parameters[0]} LIKE ${condition.parameters[1]}`\n            case \"between\":\n                return `${condition.parameters[0]} BETWEEN ${condition.parameters[1]} AND ${condition.parameters[2]}`\n            case \"in\":\n                if (condition.parameters.length <= 1) {\n                    return \"0=1\"\n                }\n                return `${condition.parameters[0]} IN (${condition.parameters\n                    .slice(1)\n                    .join(\", \")})`\n            case \"any\":\n                if (driver.options.type === \"cockroachdb\") {\n                    return `${condition.parameters[0]}::STRING = ANY(${condition.parameters[1]}::STRING[])`\n                }\n\n                return `${condition.parameters[0]} = ANY(${condition.parameters[1]})`\n            case \"isNull\":\n                return `${condition.parameters[0]} IS NULL`\n\n            case \"not\":\n                return `NOT(${this.createWhereConditionExpression(\n                    condition.condition,\n                )})`\n            case \"brackets\":\n                return `${this.createWhereConditionExpression(\n                    condition.condition,\n                    true,\n                )}`\n            case \"and\":\n                return \"(\" + condition.parameters.join(\" AND \") + \")\"\n            case \"or\":\n                return \"(\" + condition.parameters.join(\" OR \") + \")\"\n        }\n\n        throw new TypeError(\n            `Unsupported FindOperator ${FindOperator.constructor.name}`,\n        )\n    }\n\n    protected createCteExpression(): string {\n        if (!this.hasCommonTableExpressions()) {\n            return \"\"\n        }\n        const databaseRequireRecusiveHint =\n            this.connection.driver.cteCapabilities.requiresRecursiveHint\n\n        const cteStrings = this.expressionMap.commonTableExpressions.map(\n            (cte) => {\n                let cteBodyExpression =\n                    typeof cte.queryBuilder === \"string\" ? cte.queryBuilder : \"\"\n                if (typeof cte.queryBuilder !== \"string\") {\n                    if (cte.queryBuilder.hasCommonTableExpressions()) {\n                        throw new TypeORMError(\n                            `Nested CTEs aren't supported (CTE: ${cte.alias})`,\n                        )\n                    }\n                    cteBodyExpression = cte.queryBuilder.getQuery()\n                    if (\n                        !this.connection.driver.cteCapabilities.writable &&\n                        !InstanceChecker.isSelectQueryBuilder(cte.queryBuilder)\n                    ) {\n                        throw new TypeORMError(\n                            `Only select queries are supported in CTEs in ${this.connection.options.type} (CTE: ${cte.alias})`,\n                        )\n                    }\n                    this.setParameters(cte.queryBuilder.getParameters())\n                }\n                let cteHeader = this.escape(cte.alias)\n                if (cte.options.columnNames) {\n                    const escapedColumnNames = cte.options.columnNames.map(\n                        (column) => this.escape(column),\n                    )\n                    if (\n                        InstanceChecker.isSelectQueryBuilder(cte.queryBuilder)\n                    ) {\n                        if (\n                            cte.queryBuilder.expressionMap.selects.length &&\n                            cte.options.columnNames.length !==\n                                cte.queryBuilder.expressionMap.selects.length\n                        ) {\n                            throw new TypeORMError(\n                                `cte.options.columnNames length (${cte.options.columnNames.length}) doesn't match subquery select list length ${cte.queryBuilder.expressionMap.selects.length} (CTE: ${cte.alias})`,\n                            )\n                        }\n                    }\n                    cteHeader += `(${escapedColumnNames.join(\", \")})`\n                }\n                const recursiveClause =\n                    cte.options.recursive && databaseRequireRecusiveHint\n                        ? \"RECURSIVE\"\n                        : \"\"\n                let materializeClause = \"\"\n                if (\n                    this.connection.driver.cteCapabilities.materializedHint &&\n                    cte.options.materialized !== undefined\n                ) {\n                    materializeClause = cte.options.materialized\n                        ? \"MATERIALIZED\"\n                        : \"NOT MATERIALIZED\"\n                }\n\n                return [\n                    recursiveClause,\n                    cteHeader,\n                    \"AS\",\n                    materializeClause,\n                    `(${cteBodyExpression})`,\n                ]\n                    .filter(Boolean)\n                    .join(\" \")\n            },\n        )\n\n        return \"WITH \" + cteStrings.join(\", \") + \" \"\n    }\n\n    /**\n     * Creates \"WHERE\" condition for an in-ids condition.\n     */\n    protected getWhereInIdsCondition(\n        ids: any | any[],\n    ): ObjectLiteral | Brackets {\n        const metadata = this.expressionMap.mainAlias!.metadata\n        const normalized = (Array.isArray(ids) ? ids : [ids]).map((id) =>\n            metadata.ensureEntityIdMap(id),\n        )\n\n        // using in(...ids) for single primary key entities\n        if (!metadata.hasMultiplePrimaryKeys) {\n            const primaryColumn = metadata.primaryColumns[0]\n\n            // getEntityValue will try to transform `In`, it is a bug\n            // todo: remove this transformer check after #2390 is fixed\n            // This also fails for embedded & relation, so until that is fixed skip it.\n            if (\n                !primaryColumn.transformer &&\n                !primaryColumn.relationMetadata &&\n                !primaryColumn.embeddedMetadata\n            ) {\n                return {\n                    [primaryColumn.propertyName]: In(\n                        normalized.map((id) =>\n                            primaryColumn.getEntityValue(id, false),\n                        ),\n                    ),\n                }\n            }\n        }\n\n        return new Brackets((qb) => {\n            for (const data of normalized) {\n                qb.orWhere(new Brackets((qb) => qb.where(data)))\n            }\n        })\n    }\n\n    protected getExistsCondition(subQuery: any): [string, any[]] {\n        const query = subQuery\n            .clone()\n            .orderBy()\n            .groupBy()\n            .offset(undefined)\n            .limit(undefined)\n            .skip(undefined)\n            .take(undefined)\n            .select(\"1\")\n            .setOption(\"disable-global-order\")\n\n        return [`EXISTS (${query.getQuery()})`, query.getParameters()]\n    }\n\n    private findColumnsForPropertyPath(\n        propertyPath: string,\n    ): [Alias, string[], ColumnMetadata[]] {\n        // Make a helper to iterate the entity & relations?\n        // Use that to set the correct alias?  Or the other way around?\n\n        // Start with the main alias with our property paths\n        let alias = this.expressionMap.mainAlias\n        const root: string[] = []\n        const propertyPathParts = propertyPath.split(\".\")\n\n        while (propertyPathParts.length > 1) {\n            const part = propertyPathParts[0]\n\n            if (!alias?.hasMetadata) {\n                // If there's no metadata, we're wasting our time\n                // and can't actually look any of this up.\n                break\n            }\n\n            if (alias.metadata.hasEmbeddedWithPropertyPath(part)) {\n                // If this is an embedded then we should combine the two as part of our lookup.\n                // Instead of just breaking, we keep going with this in case there's an embedded/relation\n                // inside an embedded.\n                propertyPathParts.unshift(\n                    `${propertyPathParts.shift()}.${propertyPathParts.shift()}`,\n                )\n                continue\n            }\n\n            if (alias.metadata.hasRelationWithPropertyPath(part)) {\n                // If this is a relation then we should find the aliases\n                // that match the relation & then continue further down\n                // the property path\n                const joinAttr = this.expressionMap.joinAttributes.find(\n                    (joinAttr) => joinAttr.relationPropertyPath === part,\n                )\n\n                if (!joinAttr?.alias) {\n                    const fullRelationPath =\n                        root.length > 0 ? `${root.join(\".\")}.${part}` : part\n                    throw new Error(\n                        `Cannot find alias for relation at ${fullRelationPath}`,\n                    )\n                }\n\n                alias = joinAttr.alias\n                root.push(...part.split(\".\"))\n                propertyPathParts.shift()\n                continue\n            }\n\n            break\n        }\n\n        if (!alias) {\n            throw new Error(`Cannot find alias for property ${propertyPath}`)\n        }\n\n        // Remaining parts are combined back and used to find the actual property path\n        const aliasPropertyPath = propertyPathParts.join(\".\")\n\n        const columns =\n            alias.metadata.findColumnsWithPropertyPath(aliasPropertyPath)\n\n        if (!columns.length) {\n            throw new EntityPropertyNotFoundError(propertyPath, alias.metadata)\n        }\n\n        return [alias, root, columns]\n    }\n\n    /**\n     * Creates a property paths for a given ObjectLiteral.\n     */\n    protected createPropertyPath(\n        metadata: EntityMetadata,\n        entity: ObjectLiteral,\n        prefix: string = \"\",\n    ) {\n        const paths: string[] = []\n\n        for (const key of Object.keys(entity)) {\n            const path = prefix ? `${prefix}.${key}` : key\n\n            // There's times where we don't actually want to traverse deeper.\n            // If the value is a `FindOperator`, or null, or not an object, then we don't, for example.\n            if (\n                entity[key] === null ||\n                typeof entity[key] !== \"object\" ||\n                InstanceChecker.isFindOperator(entity[key])\n            ) {\n                paths.push(path)\n                continue\n            }\n\n            if (metadata.hasEmbeddedWithPropertyPath(path)) {\n                const subPaths = this.createPropertyPath(\n                    metadata,\n                    entity[key],\n                    path,\n                )\n                paths.push(...subPaths)\n                continue\n            }\n\n            if (metadata.hasRelationWithPropertyPath(path)) {\n                const relation = metadata.findRelationWithPropertyPath(path)!\n\n                // There's also cases where we don't want to return back all of the properties.\n                // These handles the situation where someone passes the model & we don't need to make\n                // a HUGE `where` to uniquely look up the entity.\n\n                // In the case of a *-to-one, there's only ever one possible entity on the other side\n                // so if the join columns are all defined we can return just the relation itself\n                // because it will fetch only the join columns and do the lookup.\n                if (\n                    relation.relationType === \"one-to-one\" ||\n                    relation.relationType === \"many-to-one\"\n                ) {\n                    const joinColumns = relation.joinColumns\n                        .map((j) => j.referencedColumn)\n                        .filter((j): j is ColumnMetadata => !!j)\n\n                    const hasAllJoinColumns =\n                        joinColumns.length > 0 &&\n                        joinColumns.every((column) =>\n                            column.getEntityValue(entity[key], false),\n                        )\n\n                    if (hasAllJoinColumns) {\n                        paths.push(path)\n                        continue\n                    }\n                }\n\n                if (\n                    relation.relationType === \"one-to-many\" ||\n                    relation.relationType === \"many-to-many\"\n                ) {\n                    throw new Error(\n                        `Cannot query across ${relation.relationType} for property ${path}`,\n                    )\n                }\n\n                // For any other case, if the `entity[key]` contains all of the primary keys we can do a\n                // lookup via these.  We don't need to look up via any other values 'cause these are\n                // the unique primary keys.\n                // This handles the situation where someone passes the model & we don't need to make\n                // a HUGE where.\n                const primaryColumns =\n                    relation.inverseEntityMetadata.primaryColumns\n                const hasAllPrimaryKeys =\n                    primaryColumns.length > 0 &&\n                    primaryColumns.every((column) =>\n                        column.getEntityValue(entity[key], false),\n                    )\n\n                if (hasAllPrimaryKeys) {\n                    const subPaths = primaryColumns.map(\n                        (column) => `${path}.${column.propertyPath}`,\n                    )\n                    paths.push(...subPaths)\n                    continue\n                }\n\n                // If nothing else, just return every property that's being passed to us.\n                const subPaths = this.createPropertyPath(\n                    relation.inverseEntityMetadata,\n                    entity[key],\n                ).map((p) => `${path}.${p}`)\n                paths.push(...subPaths)\n                continue\n            }\n\n            paths.push(path)\n        }\n\n        return paths\n    }\n\n    protected *getPredicates(where: ObjectLiteral) {\n        if (this.expressionMap.mainAlias!.hasMetadata) {\n            const propertyPaths = this.createPropertyPath(\n                this.expressionMap.mainAlias!.metadata,\n                where,\n            )\n\n            for (const propertyPath of propertyPaths) {\n                const [alias, aliasPropertyPath, columns] =\n                    this.findColumnsForPropertyPath(propertyPath)\n\n                for (const column of columns) {\n                    let containedWhere = where\n\n                    for (const part of aliasPropertyPath) {\n                        if (!containedWhere || !(part in containedWhere)) {\n                            containedWhere = {}\n                            break\n                        }\n\n                        containedWhere = containedWhere[part]\n                    }\n\n                    // Use the correct alias & the property path from the column\n                    const aliasPath = this.expressionMap\n                        .aliasNamePrefixingEnabled\n                        ? `${alias.name}.${column.propertyPath}`\n                        : column.propertyPath\n\n                    const parameterValue = column.getEntityValue(\n                        containedWhere,\n                        true,\n                    )\n\n                    yield [aliasPath, parameterValue]\n                }\n            }\n        } else {\n            for (const key of Object.keys(where)) {\n                const parameterValue = where[key]\n                const aliasPath = this.expressionMap.aliasNamePrefixingEnabled\n                    ? `${this.alias}.${key}`\n                    : key\n\n                yield [aliasPath, parameterValue]\n            }\n        }\n    }\n\n    protected getWherePredicateCondition(\n        aliasPath: string,\n        parameterValue: any,\n    ): WhereClauseCondition {\n        if (InstanceChecker.isFindOperator(parameterValue)) {\n            const parameters: any[] = []\n            if (parameterValue.useParameter) {\n                if (parameterValue.objectLiteralParameters) {\n                    this.setParameters(parameterValue.objectLiteralParameters)\n                } else if (parameterValue.multipleParameters) {\n                    for (const v of parameterValue.value) {\n                        parameters.push(this.createParameter(v))\n                    }\n                } else {\n                    parameters.push(this.createParameter(parameterValue.value))\n                }\n            }\n\n            if (parameterValue.type === \"raw\") {\n                if (parameterValue.getSql) {\n                    return parameterValue.getSql(aliasPath)\n                } else {\n                    return {\n                        operator: \"equal\",\n                        parameters: [aliasPath, parameterValue.value],\n                    }\n                }\n            } else if (parameterValue.type === \"not\") {\n                if (parameterValue.child) {\n                    return {\n                        operator: parameterValue.type,\n                        condition: this.getWherePredicateCondition(\n                            aliasPath,\n                            parameterValue.child,\n                        ),\n                    }\n                } else {\n                    return {\n                        operator: \"notEqual\",\n                        parameters: [aliasPath, ...parameters],\n                    }\n                }\n            } else if (parameterValue.type === \"and\") {\n                const values: FindOperator<any>[] = parameterValue.value\n\n                return {\n                    operator: parameterValue.type,\n                    parameters: values.map((operator) =>\n                        this.createWhereConditionExpression(\n                            this.getWherePredicateCondition(\n                                aliasPath,\n                                operator,\n                            ),\n                        ),\n                    ),\n                }\n            } else if (parameterValue.type === \"or\") {\n                const values: FindOperator<any>[] = parameterValue.value\n\n                return {\n                    operator: parameterValue.type,\n                    parameters: values.map((operator) =>\n                        this.createWhereConditionExpression(\n                            this.getWherePredicateCondition(\n                                aliasPath,\n                                operator,\n                            ),\n                        ),\n                    ),\n                }\n            } else {\n                return {\n                    operator: parameterValue.type,\n                    parameters: [aliasPath, ...parameters],\n                }\n            }\n            // } else if (parameterValue === null) {\n            //     return {\n            //         operator: \"isNull\",\n            //         parameters: [\n            //             aliasPath,\n            //         ]\n            //     };\n        } else {\n            return {\n                operator: \"equal\",\n                parameters: [aliasPath, this.createParameter(parameterValue)],\n            }\n        }\n    }\n\n    protected getWhereCondition(\n        where:\n            | string\n            | ((qb: this) => string)\n            | Brackets\n            | NotBrackets\n            | ObjectLiteral\n            | ObjectLiteral[],\n    ): WhereClauseCondition {\n        if (typeof where === \"string\") {\n            return where\n        }\n\n        if (InstanceChecker.isBrackets(where)) {\n            const whereQueryBuilder = this.createQueryBuilder()\n\n            whereQueryBuilder.parentQueryBuilder = this\n\n            whereQueryBuilder.expressionMap.mainAlias =\n                this.expressionMap.mainAlias\n            whereQueryBuilder.expressionMap.aliasNamePrefixingEnabled =\n                this.expressionMap.aliasNamePrefixingEnabled\n            whereQueryBuilder.expressionMap.parameters =\n                this.expressionMap.parameters\n            whereQueryBuilder.expressionMap.nativeParameters =\n                this.expressionMap.nativeParameters\n\n            whereQueryBuilder.expressionMap.wheres = []\n\n            where.whereFactory(whereQueryBuilder as any)\n\n            return {\n                operator: InstanceChecker.isNotBrackets(where)\n                    ? \"not\"\n                    : \"brackets\",\n                condition: whereQueryBuilder.expressionMap.wheres,\n            }\n        }\n\n        if (typeof where === \"function\") {\n            return where(this)\n        }\n\n        const wheres: ObjectLiteral[] = Array.isArray(where) ? where : [where]\n        const clauses: WhereClause[] = []\n\n        for (const where of wheres) {\n            const conditions: WhereClauseCondition = []\n\n            // Filter the conditions and set up the parameter values\n            for (const [aliasPath, parameterValue] of this.getPredicates(\n                where,\n            )) {\n                conditions.push({\n                    type: \"and\",\n                    condition: this.getWherePredicateCondition(\n                        aliasPath,\n                        parameterValue,\n                    ),\n                })\n            }\n\n            clauses.push({ type: \"or\", condition: conditions })\n        }\n\n        if (clauses.length === 1) {\n            return clauses[0].condition\n        }\n\n        return clauses\n    }\n\n    /**\n     * Creates a query builder used to execute sql queries inside this query builder.\n     */\n    protected obtainQueryRunner() {\n        return this.queryRunner || this.connection.createQueryRunner()\n    }\n\n    protected hasCommonTableExpressions(): boolean {\n        return this.expressionMap.commonTableExpressions.length > 0\n    }\n}\n"], "sourceRoot": ".."}