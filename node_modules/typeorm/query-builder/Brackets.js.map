{"version": 3, "sources": ["../../src/query-builder/Brackets.ts"], "names": [], "mappings": ";;;AAEA;;;GAGG;AACH,MAAa,QAAQ;IAQjB;;OAEG;IACH,YAAY,YAAiD;QAVpD,mBAAa,GAAG,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,CAAA;QAW3C,IAAI,CAAC,YAAY,GAAG,YAAY,CAAA;IACpC,CAAC;CACJ;AAdD,4BAcC", "file": "Brackets.js", "sourcesContent": ["import { WhereExpressionBuilder } from \"./WhereExpressionBuilder\"\n\n/**\n * Syntax sugar.\n * Allows to use brackets in WHERE expressions for better syntax.\n */\nexport class Brackets {\n    readonly \"@instanceof\" = Symbol.for(\"Brackets\")\n\n    /**\n     * WHERE expression that will be taken into brackets.\n     */\n    whereFactory: (qb: WhereExpressionBuilder) => any\n\n    /**\n     * Given WHERE query builder that will build a WHERE expression that will be taken into brackets.\n     */\n    constructor(whereFactory: (qb: WhereExpressionBuilder) => any) {\n        this.whereFactory = whereFactory\n    }\n}\n"], "sourceRoot": ".."}