{"version": 3, "sources": ["../../src/subscriber/event/RecoverEvent.ts"], "names": [], "mappings": "", "file": "RecoverEvent.js", "sourcesContent": ["import { RemoveEvent } from \"./RemoveEvent\"\n\n/**\n * RecoverEvent is an object that broadcaster sends to the entity subscriber when entity is being recovered in the database.\n */\nexport interface RecoverEvent<Entity> extends RemoveEvent<Entity> {}\n"], "sourceRoot": "../.."}