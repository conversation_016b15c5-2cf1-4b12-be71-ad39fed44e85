{"version": 3, "sources": ["../../src/cache/RedisQueryResultCache.ts"], "names": [], "mappings": ";;;AAEA,6DAAyD;AAGzD,wDAAoD;AAEpD;;GAEG;AACH,MAAa,qBAAqB;IAyB9B,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YACc,UAAsB,EAChC,UAAmD;QADzC,eAAU,GAAV,UAAU,CAAY;QAGhC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAA;QAC5B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,EAAE,CAAA;IACjC,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,KAAK,CAAC,OAAO;QACT,MAAM,YAAY,GAAQ,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAA;QACvD,IAAI,IAAI,CAAC,UAAU,KAAK,OAAO,EAAE,CAAC;YAC9B,MAAM,aAAa,GAAG;gBAClB,GAAG,YAAY,EAAE,OAAO;aAC3B,CAAA;YAED,8CAA8C;YAC9C,IAAI,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,aAAa,CAAC,CAAA;YACvD,MAAM,YAAY,GAAG,OAAO,UAAU,CAAC,OAAO,KAAK,UAAU,CAAA;YAE7D,IAAI,YAAY,EAAE,CAAC;gBACf,4DAA4D;gBAC5D,iDAAiD;gBACjD,aAAa,CAAC,UAAU,GAAG,IAAI,CAAA;gBAC/B,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,aAAa,CAAC,CAAA;YACvD,CAAC;YAED,yBAAyB;YACzB,IAAI,CAAC,MAAM,GAAG,UAAU,CAAA;YAExB,IACI,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,KAAK,QAAQ;gBACjD,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,YAAY,EAC5C,CAAC;gBACC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAQ,EAAE,EAAE;oBACjC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;gBAC3C,CAAC,CAAC,CAAA;YACN,CAAC;YAED,sBAAsB;YACtB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,KAAK,UAAU,EAAE,CAAC;gBAC5C,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAA;YAC/B,CAAC;YAED,yDAAyD;YACzD,IAAI,CAAC,kBAAkB,EAAE,CAAA;QAC7B,CAAC;aAAM,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;YACvC,IAAI,YAAY,IAAI,YAAY,CAAC,IAAI,EAAE,CAAC;gBACpC,IAAI,YAAY,CAAC,OAAO,EAAE,CAAC;oBACvB,IAAI,CAAC,MAAM,GAAG,IAAI,IAAI,CAAC,KAAK,CACxB,YAAY,CAAC,IAAI,EACjB,YAAY,CAAC,OAAO,CACvB,CAAA;gBACL,CAAC;qBAAM,CAAC;oBACJ,IAAI,CAAC,MAAM,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA;gBACnD,CAAC;YACL,CAAC;iBAAM,IAAI,YAAY,IAAI,YAAY,CAAC,OAAO,EAAE,CAAC;gBAC9C,IAAI,CAAC,MAAM,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,CAAA;YACtD,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,MAAM,GAAG,IAAI,IAAI,CAAC,KAAK,EAAE,CAAA;YAClC,CAAC;QACL,CAAC;aAAM,IAAI,IAAI,CAAC,UAAU,KAAK,iBAAiB,EAAE,CAAC;YAC/C,IACI,YAAY;gBACZ,YAAY,CAAC,OAAO;gBACpB,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,EACrC,CAAC;gBACC,IAAI,CAAC,MAAM,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,CAAA;YAC9D,CAAC;iBAAM,IACH,YAAY;gBACZ,YAAY,CAAC,OAAO;gBACpB,YAAY,CAAC,OAAO,CAAC,YAAY,EACnC,CAAC;gBACC,IAAI,CAAC,MAAM,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAChC,YAAY,CAAC,OAAO,CAAC,YAAY,EACjC,YAAY,CAAC,OAAO,CAAC,OAAO,CAC/B,CAAA;YACL,CAAC;iBAAM,CAAC;gBACJ,MAAM,IAAI,2BAAY,CAClB,qCAAqC,IAAI,CAAC,UAAU,GAAG,CAC1D,CAAA;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACZ,IAAI,IAAI,CAAC,gBAAgB,EAAE,EAAE,CAAC;YAC1B,8CAA8C;YAC9C,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAA;YACxB,IAAI,CAAC,MAAM,GAAG,SAAS,CAAA;YACvB,OAAM;QACV,CAAC;QAED,2BAA2B;QAC3B,OAAO,IAAI,OAAO,CAAO,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YAClC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAQ,EAAE,MAAW,EAAE,EAAE;gBACvC,IAAI,GAAG;oBAAE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA;gBACzB,EAAE,EAAE,CAAA;gBACJ,IAAI,CAAC,MAAM,GAAG,SAAS,CAAA;YAC3B,CAAC,CAAC,CAAA;QACN,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,WAAwB,IAAkB,CAAC;IAE7D;;;;OAIG;IACH,YAAY,CACR,OAAgC,EAChC,WAAyB;QAEzB,MAAM,GAAG,GAAG,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,KAAK,CAAA;QAC/C,IAAI,CAAC,GAAG;YAAE,OAAO,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA;QAE3C,IAAI,IAAI,CAAC,gBAAgB,EAAE,EAAE,CAAC;YAC1B,6BAA6B;YAC7B,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,MAAW,EAAE,EAAE;gBAC7C,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;YAClD,CAAC,CAAC,CAAA;QACN,CAAC;QAED,+BAA+B;QAC/B,OAAO,IAAI,OAAO,CAAsC,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YACjE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,GAAQ,EAAE,MAAW,EAAE,EAAE;gBAC3C,IAAI,GAAG;oBAAE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA;gBACzB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAA;YAC/C,CAAC,CAAC,CAAA;QACN,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,UAAmC;QACzC,OAAO,UAAU,CAAC,IAAK,GAAG,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;IAC9D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CACd,OAAgC,EAChC,UAAmC,EACnC,WAAyB;QAEzB,MAAM,GAAG,GAAG,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,KAAK,CAAA;QAC/C,IAAI,CAAC,GAAG;YAAE,OAAM;QAEhB,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA;QACrC,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAA;QAEjC,IAAI,IAAI,CAAC,gBAAgB,EAAE,EAAE,CAAC;YAC1B,4CAA4C;YAC5C,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE;gBAC9B,EAAE,EAAE,QAAQ;aACf,CAAC,CAAA;YACF,OAAM;QACV,CAAC;QAED,+BAA+B;QAC/B,OAAO,IAAI,OAAO,CAAO,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YAClC,IAAI,CAAC,MAAM,CAAC,GAAG,CACX,GAAG,EACH,KAAK,EACL,IAAI,EACJ,QAAQ,EACR,CAAC,GAAQ,EAAE,MAAW,EAAE,EAAE;gBACtB,IAAI,GAAG;oBAAE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA;gBACzB,EAAE,EAAE,CAAA;YACR,CAAC,CACJ,CAAA;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK,CAAC,WAAyB;QACjC,IAAI,IAAI,CAAC,gBAAgB,EAAE,EAAE,CAAC;YAC1B,6BAA6B;YAC7B,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAA;YAC3B,OAAM;QACV,CAAC;QAED,+BAA+B;QAC/B,OAAO,IAAI,OAAO,CAAO,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YAClC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,GAAQ,EAAE,MAAW,EAAE,EAAE;gBAC1C,IAAI,GAAG;oBAAE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA;gBACzB,EAAE,EAAE,CAAA;YACR,CAAC,CAAC,CAAA;QACN,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM,CACR,WAAqB,EACrB,WAAyB;QAEzB,MAAM,OAAO,CAAC,GAAG,CACb,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;YAC3B,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAA;QACrC,CAAC,CAAC,CACL,CAAA;IACL,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;OAEG;IACO,KAAK,CAAC,SAAS,CAAC,GAAW;QACjC,IAAI,IAAI,CAAC,gBAAgB,EAAE,EAAE,CAAC;YAC1B,6BAA6B;YAC7B,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;YAC1B,OAAM;QACV,CAAC;QAED,+BAA+B;QAC/B,OAAO,IAAI,OAAO,CAAO,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YAClC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,GAAQ,EAAE,MAAW,EAAE,EAAE;gBAC3C,IAAI,GAAG;oBAAE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA;gBACzB,EAAE,EAAE,CAAA;YACR,CAAC,CAAC,CAAA;QACN,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACO,SAAS;QACf,IAAI,CAAC;YACD,IAAI,IAAI,CAAC,UAAU,KAAK,iBAAiB,EAAE,CAAC;gBACxC,OAAO,6BAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;YACxC,CAAC;iBAAM,CAAC;gBACJ,OAAO,6BAAa,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;YAC9C,CAAC;QACL,CAAC;QAAC,MAAM,CAAC;YACL,MAAM,IAAI,2BAAY,CAClB,4BAA4B,IAAI,CAAC,UAAU,wCAAwC,IAAI,CAAC,UAAU,IAAI,CACzG,CAAA;QACL,CAAC;IACL,CAAC;IAED;;;OAGG;IACK,kBAAkB;QACtB,IAAI,IAAI,CAAC,UAAU,KAAK,OAAO;YAAE,OAAM;QAEvC,IAAI,CAAC;YACD,6DAA6D;YAC7D,iDAAiD;YACjD,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAA;YACjC,IAAI,SAAS,IAAI,SAAS,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;gBACrC,qEAAqE;gBACrE,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAA;YAC9B,CAAC;iBAAM,CAAC;gBACJ,uFAAuF;gBACvF,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAA;YAC9B,CAAC;QACL,CAAC;QAAC,MAAM,CAAC;YACL,iDAAiD;YACjD,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAA;QAC9B,CAAC;IACL,CAAC;IAED;;OAEG;IACK,gBAAgB;QACpB,IAAI,IAAI,CAAC,UAAU,KAAK,OAAO;YAAE,OAAO,KAAK,CAAA;QAC7C,OAAO,CACH,IAAI,CAAC,iBAAiB,KAAK,SAAS,IAAI,IAAI,CAAC,iBAAiB,IAAI,CAAC,CACtE,CAAA;IACL,CAAC;CACJ;AApUD,sDAoUC", "file": "RedisQueryResultCache.js", "sourcesContent": ["import { QueryResultCache } from \"./QueryResultCache\"\nimport { QueryResultCacheOptions } from \"./QueryResultCacheOptions\"\nimport { PlatformTools } from \"../platform/PlatformTools\"\nimport { DataSource } from \"../data-source/DataSource\"\nimport { QueryRunner } from \"../query-runner/QueryRunner\"\nimport { TypeORMError } from \"../error/TypeORMError\"\n\n/**\n * Caches query result into Redis database.\n */\nexport class RedisQueryResultCache implements QueryResultCache {\n    // -------------------------------------------------------------------------\n    // Protected Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Redis module instance loaded dynamically.\n     */\n    protected redis: any\n\n    /**\n     * Connected redis client.\n     */\n    protected client: any\n\n    /**\n     * Type of the Redis Client (redis or ioredis).\n     */\n    protected clientType: \"redis\" | \"ioredis\" | \"ioredis/cluster\"\n\n    /**\n     * Redis major version number\n     */\n    protected redisMajorVersion: number | undefined\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(\n        protected connection: DataSource,\n        clientType: \"redis\" | \"ioredis\" | \"ioredis/cluster\",\n    ) {\n        this.clientType = clientType\n        this.redis = this.loadRedis()\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates a connection with given cache provider.\n     */\n    async connect(): Promise<void> {\n        const cacheOptions: any = this.connection.options.cache\n        if (this.clientType === \"redis\") {\n            const clientOptions = {\n                ...cacheOptions?.options,\n            }\n\n            // Create initial client to test Redis version\n            let tempClient = this.redis.createClient(clientOptions)\n            const isRedis4Plus = typeof tempClient.connect === \"function\"\n\n            if (isRedis4Plus) {\n                // Redis 4+ detected, recreate with legacyMode for Redis 4.x\n                // (Redis 5 will ignore legacyMode if not needed)\n                clientOptions.legacyMode = true\n                tempClient = this.redis.createClient(clientOptions)\n            }\n\n            // Set as the main client\n            this.client = tempClient\n\n            if (\n                typeof this.connection.options.cache === \"object\" &&\n                this.connection.options.cache.ignoreErrors\n            ) {\n                this.client.on(\"error\", (err: any) => {\n                    this.connection.logger.log(\"warn\", err)\n                })\n            }\n\n            // Connect if Redis 4+\n            if (typeof this.client.connect === \"function\") {\n                await this.client.connect()\n            }\n\n            // Detect precise version after connection is established\n            this.detectRedisVersion()\n        } else if (this.clientType === \"ioredis\") {\n            if (cacheOptions && cacheOptions.port) {\n                if (cacheOptions.options) {\n                    this.client = new this.redis(\n                        cacheOptions.port,\n                        cacheOptions.options,\n                    )\n                } else {\n                    this.client = new this.redis(cacheOptions.port)\n                }\n            } else if (cacheOptions && cacheOptions.options) {\n                this.client = new this.redis(cacheOptions.options)\n            } else {\n                this.client = new this.redis()\n            }\n        } else if (this.clientType === \"ioredis/cluster\") {\n            if (\n                cacheOptions &&\n                cacheOptions.options &&\n                Array.isArray(cacheOptions.options)\n            ) {\n                this.client = new this.redis.Cluster(cacheOptions.options)\n            } else if (\n                cacheOptions &&\n                cacheOptions.options &&\n                cacheOptions.options.startupNodes\n            ) {\n                this.client = new this.redis.Cluster(\n                    cacheOptions.options.startupNodes,\n                    cacheOptions.options.options,\n                )\n            } else {\n                throw new TypeORMError(\n                    `options.startupNodes required for ${this.clientType}.`,\n                )\n            }\n        }\n    }\n\n    /**\n     * Disconnects the connection\n     */\n    async disconnect(): Promise<void> {\n        if (this.isRedis5OrHigher()) {\n            // Redis 5+ uses quit() that returns a Promise\n            await this.client.quit()\n            this.client = undefined\n            return\n        }\n\n        // Redis 3/4 callback style\n        return new Promise<void>((ok, fail) => {\n            this.client.quit((err: any, result: any) => {\n                if (err) return fail(err)\n                ok()\n                this.client = undefined\n            })\n        })\n    }\n\n    /**\n     * Creates table for storing cache if it does not exist yet.\n     */\n    async synchronize(queryRunner: QueryRunner): Promise<void> {}\n\n    /**\n     * Get data from cache.\n     * Returns cache result if found.\n     * Returns undefined if result is not cached.\n     */\n    getFromCache(\n        options: QueryResultCacheOptions,\n        queryRunner?: QueryRunner,\n    ): Promise<QueryResultCacheOptions | undefined> {\n        const key = options.identifier || options.query\n        if (!key) return Promise.resolve(undefined)\n\n        if (this.isRedis5OrHigher()) {\n            // Redis 5+ Promise-based API\n            return this.client.get(key).then((result: any) => {\n                return result ? JSON.parse(result) : undefined\n            })\n        }\n\n        // Redis 3/4 callback-based API\n        return new Promise<QueryResultCacheOptions | undefined>((ok, fail) => {\n            this.client.get(key, (err: any, result: any) => {\n                if (err) return fail(err)\n                ok(result ? JSON.parse(result) : undefined)\n            })\n        })\n    }\n\n    /**\n     * Checks if cache is expired or not.\n     */\n    isExpired(savedCache: QueryResultCacheOptions): boolean {\n        return savedCache.time! + savedCache.duration < Date.now()\n    }\n\n    /**\n     * Stores given query result in the cache.\n     */\n    async storeInCache(\n        options: QueryResultCacheOptions,\n        savedCache: QueryResultCacheOptions,\n        queryRunner?: QueryRunner,\n    ): Promise<void> {\n        const key = options.identifier || options.query\n        if (!key) return\n\n        const value = JSON.stringify(options)\n        const duration = options.duration\n\n        if (this.isRedis5OrHigher()) {\n            // Redis 5+ Promise-based API with PX option\n            await this.client.set(key, value, {\n                PX: duration,\n            })\n            return\n        }\n\n        // Redis 3/4 callback-based API\n        return new Promise<void>((ok, fail) => {\n            this.client.set(\n                key,\n                value,\n                \"PX\",\n                duration,\n                (err: any, result: any) => {\n                    if (err) return fail(err)\n                    ok()\n                },\n            )\n        })\n    }\n\n    /**\n     * Clears everything stored in the cache.\n     */\n    async clear(queryRunner?: QueryRunner): Promise<void> {\n        if (this.isRedis5OrHigher()) {\n            // Redis 5+ Promise-based API\n            await this.client.flushDb()\n            return\n        }\n\n        // Redis 3/4 callback-based API\n        return new Promise<void>((ok, fail) => {\n            this.client.flushdb((err: any, result: any) => {\n                if (err) return fail(err)\n                ok()\n            })\n        })\n    }\n\n    /**\n     * Removes all cached results by given identifiers from cache.\n     */\n    async remove(\n        identifiers: string[],\n        queryRunner?: QueryRunner,\n    ): Promise<void> {\n        await Promise.all(\n            identifiers.map((identifier) => {\n                return this.deleteKey(identifier)\n            }),\n        )\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Removes a single key from redis database.\n     */\n    protected async deleteKey(key: string): Promise<void> {\n        if (this.isRedis5OrHigher()) {\n            // Redis 5+ Promise-based API\n            await this.client.del(key)\n            return\n        }\n\n        // Redis 3/4 callback-based API\n        return new Promise<void>((ok, fail) => {\n            this.client.del(key, (err: any, result: any) => {\n                if (err) return fail(err)\n                ok()\n            })\n        })\n    }\n\n    /**\n     * Loads redis dependency.\n     */\n    protected loadRedis(): any {\n        try {\n            if (this.clientType === \"ioredis/cluster\") {\n                return PlatformTools.load(\"ioredis\")\n            } else {\n                return PlatformTools.load(this.clientType)\n            }\n        } catch {\n            throw new TypeORMError(\n                `Cannot use cache because ${this.clientType} is not installed. Please run \"npm i ${this.clientType}\".`,\n            )\n        }\n    }\n\n    /**\n     * Detects the Redis version based on the connected client's API characteristics\n     * without creating test keys in the database\n     */\n    private detectRedisVersion(): void {\n        if (this.clientType !== \"redis\") return\n\n        try {\n            // Detect version by examining the client's method signatures\n            // This avoids creating test keys in the database\n            const setMethod = this.client.set\n            if (setMethod && setMethod.length <= 3) {\n                // Redis 5+ set method accepts fewer parameters (key, value, options)\n                this.redisMajorVersion = 5\n            } else {\n                // Redis 3/4 set method requires more parameters (key, value, flag, duration, callback)\n                this.redisMajorVersion = 3\n            }\n        } catch {\n            // Default to Redis 3/4 for maximum compatibility\n            this.redisMajorVersion = 3\n        }\n    }\n\n    /**\n     * Checks if Redis version is 5.x or higher\n     */\n    private isRedis5OrHigher(): boolean {\n        if (this.clientType !== \"redis\") return false\n        return (\n            this.redisMajorVersion !== undefined && this.redisMajorVersion >= 5\n        )\n    }\n}\n"], "sourceRoot": ".."}