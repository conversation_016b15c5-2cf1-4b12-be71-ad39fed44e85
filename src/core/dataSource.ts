import { IAttibutes, IDbConfig, IEntitySchemaOptions } from "../types";
import { EntitySchema } from "./entitySchema";
import { SequelizerAdaptor } from "../adaptors";

type TypedAttributes<T extends IAttibutes> = {
  readonly [K in keyof T]: T[K];
};

export interface TypedModel<T extends IAttibutes> extends EntitySchema {
  attributes: TypedAttributes<T>;
}

export class DataSource {
  private dbConfig = {};
  private sequelizerAdaptor: SequelizerAdaptor;

  constructor(dbConfig: IDbConfig) {
    this.dbConfig = dbConfig;
    this.sequelizerAdaptor = new SequelizerAdaptor(dbConfig);
    this.setupSequelizer(dbConfig);
  }

  private setupSequelizer(dbConfig: IDbConfig) {
    if (dbConfig.entities) {
      dbConfig.entities.forEach((entity) => {
        this.sequelizerAdaptor.define(
          entity.getEntityName(),
          entity.getAttributes(),
          entity.getOptions()
        );
      });
    }
  }

  public defineEntity<T extends IAttibutes>(
    entityName: string,
    attributes: T,
    options?: IEntitySchemaOptions
  ): TypedModel<T> {
    this.sequelizerAdaptor.define(entityName, attributes, options);
    const model = new EntitySchema(entityName, attributes, options);
    return model as TypedModel<T>;
  }

  public getDbConfig() {
    return this.dbConfig;
  }
}
