import { IExpressRouterConfig, IMethod } from "../../types";
import { Router, Express } from "express";

export class ExpressRouter {
  private config: IExpressRouterConfig;
  private app: Express;

  constructor(app: Express, config: IExpressRouterConfig) {
    this.config = config;
    this.app = app;
    this.setUpRouters(config);
  }

  private setUpRouters(config: IExpressRouterConfig) {
    config.controllers.forEach((controller) => {
      const router = Router();
      const allowedMethods: IMethod[] = controller.getAllowedMethod();
      const endpoint = controller.getEndpoint();
      console.log("------allowedMethods--->", allowedMethods);
      allowedMethods.forEach((method: IMethod) => {
        if (method === "get") {
          console.log("------");
          router.get("/", (req, res) => {
            const query = controller.get(req.query);
            res.send(query);
          });
          return;
        }
        if (method === "post") {
          router.post("/", (req, res) => {
            const query = controller.post(req.query);
            res.send(query);
          });
          return;
        }
        if (method === "put") {
          router.put("/", (req, res) => {
            const query = controller.put(req.query);
            res.send(query);
          });
          return;
        }
        if (method === "delete") {
          router.delete("/", (req, res) => {
            const query = controller.delete(req.query);
            res.send(query);
          });
          return;
        }
      });
      console.log("------router--->", router);
      console.log("------endpoint--2->", endpoint);
      // Ensure endpoint starts with a forward slash
      const routePath = endpoint.startsWith("/") ? endpoint : `/${endpoint}`;
      this.app.use(routePath, router);
    });
  }

  public getApp() {
    return this.app;
  }

  public getConfig() {
    return this.config;
  }
}
